class DiagramInteractionManager {
    constructor(container) {
        this.container = container;
        this.svg = container.querySelector('svg');
        this.scale = 1;
        this.isDragging = false;
        this.lastX = 0;
        this.lastY = 0;
        
        this.setupControls();
        this.setupEventListeners();
        this.setupTooltips();
    }
    
    setupControls() {
        // Create control buttons
        const controls = document.createElement('div');
        controls.className = 'diagram-controls';
        
        // Zoom controls
        const zoomControls = document.createElement('div');
        zoomControls.className = 'zoom-controls';
        
        const zoomIn = this.createButton('+', () => this.zoom(1.2));
        const zoomOut = this.createButton('-', () => this.zoom(0.8));
        const reset = this.createButton('↺', () => this.resetView());
        
        zoomControls.appendChild(zoomIn);
        zoomControls.appendChild(zoomOut);
        zoomControls.appendChild(reset);
        
        // Export controls
        const exportBtn = this.createButton('📥', () => this.exportSVG());
        
        controls.appendChild(zoomControls);
        controls.appendChild(exportBtn);
        
        this.container.appendChild(controls);
    }
    
    setupEventListeners() {
        // Pan functionality
        this.svg.addEventListener('mousedown', (e) => {
            if (e.button === 0) { // Left click only
                this.isDragging = true;
                this.lastX = e.clientX;
                this.lastY = e.clientY;
                this.svg.style.cursor = 'grabbing';
            }
        });
        
        document.addEventListener('mousemove', (e) => {
            if (this.isDragging) {
                const dx = e.clientX - this.lastX;
                const dy = e.clientY - this.lastY;
                this.pan(dx, dy);
                this.lastX = e.clientX;
                this.lastY = e.clientY;
            }
        });
        
        document.addEventListener('mouseup', () => {
            this.isDragging = false;
            this.svg.style.cursor = 'grab';
        });
        
        // Zoom with mouse wheel
        this.svg.addEventListener('wheel', (e) => {
            e.preventDefault();
            const delta = e.deltaY > 0 ? 0.9 : 1.1;
            this.zoom(delta);
        });
        
        // Node interactions
        const nodes = this.svg.querySelectorAll('.concept-node, .effect-node, .comparison-cell');
        nodes.forEach(node => {
            node.addEventListener('click', () => this.handleNodeClick(node));
            node.addEventListener('mouseenter', () => this.handleNodeHover(node, true));
            node.addEventListener('mouseleave', () => this.handleNodeHover(node, false));
        });
    }
    
    setupTooltips() {
        const tooltipContainer = document.createElement('div');
        tooltipContainer.className = 'diagram-tooltip';
        tooltipContainer.style.display = 'none';
        this.container.appendChild(tooltipContainer);
        
        this.tooltip = tooltipContainer;
    }
    
    createButton(text, onClick) {
        const button = document.createElement('button');
        button.className = 'diagram-control-btn';
        button.textContent = text;
        button.addEventListener('click', onClick);
        return button;
    }
    
    zoom(factor) {
        this.scale *= factor;
        this.scale = Math.min(Math.max(0.5, this.scale), 3); // Limit zoom range
        this.updateTransform();
    }
    
    pan(dx, dy) {
        const transform = this.svg.style.transform || '';
        const match = transform.match(/translate\(([-\d.]+)px,\s*([-\d.]+)px\)/);
        const [currentX, currentY] = match 
            ? [parseFloat(match[1]), parseFloat(match[2])]
            : [0, 0];
            
        this.updateTransform(currentX + dx, currentY + dy);
    }
    
    updateTransform(x = 0, y = 0) {
        this.svg.style.transform = `translate(${x}px, ${y}px) scale(${this.scale})`;
    }
    
    resetView() {
        this.scale = 1;
        this.updateTransform(0, 0);
    }
    
    exportSVG() {
        const svgData = this.svg.outerHTML;
        const blob = new Blob([svgData], { type: 'image/svg+xml' });
        const url = URL.createObjectURL(blob);
        
        const link = document.createElement('a');
        link.href = url;
        link.download = 'diagram.svg';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
    }
    
    handleNodeClick(node) {
        // Remove highlight from all nodes
        this.svg.querySelectorAll('.highlight-node').forEach(n => {
            n.classList.remove('highlight-node');
        });
        
        // Highlight clicked node and connected nodes
        node.classList.add('highlight-node');
        
        // Find and highlight connected nodes based on relationships
        const nodeId = node.getAttribute('data-id');
        if (nodeId) {
            const connectedNodes = this.findConnectedNodes(nodeId);
            connectedNodes.forEach(id => {
                const connectedNode = this.svg.querySelector(`[data-id="${id}"]`);
                if (connectedNode) {
                    connectedNode.classList.add('highlight-node');
                }
            });
        }
    }
    
    handleNodeHover(node, isEntering) {
        if (isEntering) {
            const content = node.getAttribute('data-content');
            if (content) {
                this.showTooltip(content, node);
            }
        } else {
            this.hideTooltip();
        }
    }
    
    showTooltip(content, node) {
        const rect = node.getBoundingClientRect();
        const containerRect = this.container.getBoundingClientRect();
        
        this.tooltip.textContent = content;
        this.tooltip.style.display = 'block';
        this.tooltip.style.left = `${rect.left - containerRect.left + rect.width/2}px`;
        this.tooltip.style.top = `${rect.top - containerRect.top - this.tooltip.offsetHeight - 10}px`;
    }
    
    hideTooltip() {
        this.tooltip.style.display = 'none';
    }
    
    findConnectedNodes(nodeId) {
        // This method should be implemented based on your data structure
        // Return an array of connected node IDs
        return [];
    }
}

// Initialize interaction manager for each diagram
document.addEventListener('DOMContentLoaded', () => {
    const diagrams = document.querySelectorAll('.diagram-container');
    diagrams.forEach(diagram => {
        new DiagramInteractionManager(diagram);
    });
}); 