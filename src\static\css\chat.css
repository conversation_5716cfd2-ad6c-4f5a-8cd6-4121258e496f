/* Modern Chat Interface Styles - Enhanced Contrast & Readability */

/* CSS Variables for Light/Dark Mode */
:root {
    /* Light Mode Colors */
    --chat-bg-light: #ffffff;
    --chat-overlay-light: rgba(255, 255, 255, 0.95);
    --chat-header-light: #f8fafc;
    --chat-border-light: #e2e8f0;
    --chat-text-primary-light: #1a202c;
    --chat-text-secondary-light: #4a5568;
    --chat-input-bg-light: #ffffff;
    --chat-input-border-light: #cbd5e0;
    --chat-message-user-light: #3182ce;
    --chat-message-ai-light: #f7fafc;
    --chat-message-ai-text-light: #2d3748;
    --chat-shadow-light: rgba(0, 0, 0, 0.1);
    
    /* Dark Mode Colors */
    --chat-bg-dark: #1a202c;
    --chat-overlay-dark: rgba(26, 32, 44, 0.95);
    --chat-header-dark: #2d3748;
    --chat-border-dark: #4a5568;
    --chat-text-primary-dark: #f7fafc;
    --chat-text-secondary-dark: #cbd5e0;
    --chat-input-bg-dark: #2d3748;
    --chat-input-border-dark: #4a5568;
    --chat-message-user-dark: #4299e1;
    --chat-message-ai-dark: #2d3748;
    --chat-message-ai-text-dark: #e2e8f0;
    --chat-shadow-dark: rgba(0, 0, 0, 0.3);
}

/* Default Light Mode */
#chat-interface {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--chat-overlay-light);
    backdrop-filter: blur(10px);
    display: flex;
    flex-direction: column;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: scale(0.95);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

#chat-interface.active {
    opacity: 1;
    visibility: visible;
    transform: scale(1);
}

.chat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    background: var(--chat-header-light);
    border-bottom: 1px solid var(--chat-border-light);
    box-shadow: 0 2px 4px var(--chat-shadow-light);
}

.chat-title {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.chat-title h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--chat-text-primary-light);
    margin: 0;
}

.chat-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: var(--chat-text-secondary-light);
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #10b981;
    position: relative;
}

.status-dot.online::after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: rgba(16, 185, 129, 0.2);
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    animation: pulse 2s infinite;
}

.chat-controls {
    display: flex;
    gap: 0.5rem;
}

.control-btn {
    width: 36px;
    height: 36px;
    background: transparent;
    border: 1px solid var(--chat-border-light);
    border-radius: 8px;
    cursor: pointer;
    color: var(--chat-text-secondary-light);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.control-btn:hover {
    background: var(--chat-border-light);
    color: var(--chat-text-primary-light);
    transform: translateY(-1px);
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    background: var(--chat-bg-light);
    scroll-behavior: smooth;
}

.chat-messages::-webkit-scrollbar {
    width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
    background: var(--chat-border-light);
    border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb {
    background: var(--chat-text-secondary-light);
    border-radius: 3px;
    transition: background 0.2s ease;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
    background: var(--chat-text-primary-light);
}

.chat-input-container {
    padding: 1rem 1.5rem;
    background: var(--chat-header-light);
    border-top: 1px solid var(--chat-border-light);
}

.chat-input-wrapper {
    display: flex;
    gap: 0.75rem;
    align-items: flex-end;
    position: relative;
}

#query-input {
    flex: 1;
    padding: 0.75rem 1rem;
    background: var(--chat-input-bg-light);
    border: 2px solid var(--chat-input-border-light);
    border-radius: 12px;
    font-size: 0.95rem;
    color: var(--chat-text-primary-light);
    outline: none;
    resize: none;
    min-height: 44px;
    max-height: 120px;
    transition: all 0.2s ease;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

#query-input:focus {
    border-color: #3182ce;
    box-shadow: 0 0 0 3px rgba(49, 130, 206, 0.1);
}

#query-input::placeholder {
    color: var(--chat-text-secondary-light);
}

.input-buttons {
    display: flex;
    gap: 0.5rem;
}

.input-btn {
    width: 44px;
    height: 44px;
    background: var(--chat-input-bg-light);
    border: 2px solid var(--chat-input-border-light);
    border-radius: 10px;
    cursor: pointer;
    color: var(--chat-text-secondary-light);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.input-btn:hover {
    background: var(--chat-border-light);
    border-color: #3182ce;
    color: #3182ce;
    transform: translateY(-1px);
}

.send-btn {
    width: 44px;
    height: 44px;
    background: #3182ce;
    border: none;
    border-radius: 10px;
    cursor: pointer;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.send-btn:hover:not(:disabled) {
    background: #2c5aa0;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(49, 130, 206, 0.3);
}

.send-btn:disabled {
    background: var(--chat-border-light);
    color: var(--chat-text-secondary-light);
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* Message Styles */
.message {
    max-width: 75%;
    margin-bottom: 1rem;
    animation: messageSlideIn 0.3s ease-out;
}

.message-bubble {
    padding: 0.875rem 1.125rem;
    border-radius: 16px;
    font-size: 0.95rem;
    line-height: 1.5;
    word-wrap: break-word;
}

.user-message {
    align-self: flex-end;
    margin-left: auto;
}

.user-message .message-bubble {
    background: var(--chat-message-user-light);
    color: white;
    border-bottom-right-radius: 4px;
}

.assistant-message {
    align-self: flex-start;
}

.assistant-message .message-bubble {
    background: var(--chat-message-ai-light);
    color: var(--chat-message-ai-text-light);
    border: 1px solid var(--chat-border-light);
    border-bottom-left-radius: 4px;
}

/* Loading Indicator */
.typing-indicator .message-bubble {
    background: var(--chat-message-ai-light);
    border: 1px solid var(--chat-border-light);
    padding: 1rem 1.25rem;
}

.loading-dots {
    display: flex;
    gap: 0.25rem;
    align-items: center;
}

.loading-dots span {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: var(--chat-text-secondary-light);
    animation: loadingDots 1.4s infinite ease-in-out both;
}

.loading-dots span:nth-child(1) { animation-delay: -0.32s; }
.loading-dots span:nth-child(2) { animation-delay: -0.16s; }
.loading-dots span:nth-child(3) { animation-delay: 0s; }

/* Feature Popups */
.file-preview,
.voice-recording,
.emoji-picker {
    position: absolute;
    bottom: calc(100% + 0.75rem);
    background: var(--chat-input-bg-light);
    border: 1px solid var(--chat-border-light);
    border-radius: 12px;
    padding: 1rem;
    box-shadow: 0 8px 25px var(--chat-shadow-light);
    animation: slideUp 0.2s ease-out;
    z-index: 10;
}

.file-preview {
    left: 0;
    right: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 1rem;
}

.file-preview-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    color: var(--chat-text-primary-light);
}

.file-preview-icon {
    width: 40px;
    height: 40px;
    background: var(--chat-border-light);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--chat-text-secondary-light);
}

.file-preview-details {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.file-preview-name {
    font-weight: 500;
    color: var(--chat-text-primary-light);
}

.file-preview-size {
    font-size: 0.875rem;
    color: var(--chat-text-secondary-light);
}

.file-preview-remove {
    width: 32px;
    height: 32px;
    background: #fee2e2;
    border: none;
    border-radius: 6px;
    color: #dc2626;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.file-preview-remove:hover {
    background: #fecaca;
    transform: scale(1.05);
}

/* Voice Recording */
.voice-recording {
    left: 0;
    right: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 1rem;
}

.recording-indicator {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    color: var(--chat-text-primary-light);
}

.recording-dot {
    width: 12px;
    height: 12px;
    background: #dc2626;
    border-radius: 50%;
    animation: recordingPulse 1.5s infinite;
}

.recording-timer {
    font-family: 'Courier New', monospace;
    font-size: 1rem;
    font-weight: 600;
}

.recording-controls {
    display: flex;
    gap: 0.5rem;
}

.recording-btn {
    width: 36px;
    height: 36px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.recording-stop {
    background: #fee2e2;
    color: #dc2626;
}

.recording-stop:hover {
    background: #fecaca;
    transform: scale(1.05);
}

.recording-cancel {
    background: var(--chat-border-light);
    color: var(--chat-text-secondary-light);
}

.recording-cancel:hover {
    background: var(--chat-input-border-light);
    transform: scale(1.05);
}

/* Emoji Picker */
.emoji-picker {
    right: 0;
    width: 300px;
    max-height: 350px;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.emoji-categories {
    display: flex;
    gap: 0.25rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--chat-border-light);
}

.emoji-category {
    padding: 0.5rem;
    background: transparent;
    border: none;
    border-radius: 6px;
    color: var(--chat-text-secondary-light);
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 1rem;
}

.emoji-category:hover,
.emoji-category.active {
    background: var(--chat-border-light);
    color: var(--chat-text-primary-light);
}

.emoji-grid {
    display: grid;
    grid-template-columns: repeat(8, 1fr);
    gap: 0.25rem;
    max-height: 200px;
    overflow-y: auto;
    padding-right: 0.5rem;
}

.emoji-grid::-webkit-scrollbar {
    width: 4px;
}

.emoji-grid::-webkit-scrollbar-track {
    background: var(--chat-border-light);
    border-radius: 2px;
}

.emoji-grid::-webkit-scrollbar-thumb {
    background: var(--chat-text-secondary-light);
    border-radius: 2px;
}

.emoji-item {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.125rem;
    background: transparent;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.emoji-item:hover {
    background: var(--chat-border-light);
    transform: scale(1.1);
}

/* Dark Mode Styles */
[data-theme="dark"] #chat-interface {
    background: var(--chat-overlay-dark);
}

[data-theme="dark"] .chat-header {
    background: var(--chat-header-dark);
    border-color: var(--chat-border-dark);
}

[data-theme="dark"] .chat-title h3 {
    color: var(--chat-text-primary-dark);
}

[data-theme="dark"] .chat-status {
    color: var(--chat-text-secondary-dark);
}

[data-theme="dark"] .control-btn {
    border-color: var(--chat-border-dark);
    color: var(--chat-text-secondary-dark);
}

[data-theme="dark"] .control-btn:hover {
    background: var(--chat-border-dark);
    color: var(--chat-text-primary-dark);
}

[data-theme="dark"] .chat-messages {
    background: var(--chat-bg-dark);
}

[data-theme="dark"] .chat-messages::-webkit-scrollbar-track {
    background: var(--chat-border-dark);
}

[data-theme="dark"] .chat-messages::-webkit-scrollbar-thumb {
    background: var(--chat-text-secondary-dark);
}

[data-theme="dark"] .chat-input-container {
    background: var(--chat-header-dark);
    border-color: var(--chat-border-dark);
}

[data-theme="dark"] #query-input {
    background: var(--chat-input-bg-dark);
    border-color: var(--chat-input-border-dark);
    color: var(--chat-text-primary-dark);
}

[data-theme="dark"] #query-input::placeholder {
    color: var(--chat-text-secondary-dark);
}

[data-theme="dark"] .input-btn {
    background: var(--chat-input-bg-dark);
    border-color: var(--chat-input-border-dark);
    color: var(--chat-text-secondary-dark);
}

[data-theme="dark"] .input-btn:hover {
    background: var(--chat-border-dark);
    border-color: #4299e1;
    color: #4299e1;
}

[data-theme="dark"] .send-btn {
    background: var(--chat-message-user-dark);
}

[data-theme="dark"] .send-btn:hover:not(:disabled) {
    background: #3182ce;
}

[data-theme="dark"] .send-btn:disabled {
    background: var(--chat-border-dark);
    color: var(--chat-text-secondary-dark);
}

[data-theme="dark"] .user-message .message-bubble {
    background: var(--chat-message-user-dark);
}

[data-theme="dark"] .assistant-message .message-bubble {
    background: var(--chat-message-ai-dark);
    color: var(--chat-message-ai-text-dark);
    border-color: var(--chat-border-dark);
}

[data-theme="dark"] .typing-indicator .message-bubble {
    background: var(--chat-message-ai-dark);
    border-color: var(--chat-border-dark);
}

[data-theme="dark"] .loading-dots span {
    background: var(--chat-text-secondary-dark);
}

[data-theme="dark"] .file-preview,
[data-theme="dark"] .voice-recording,
[data-theme="dark"] .emoji-picker {
    background: var(--chat-input-bg-dark);
    border-color: var(--chat-border-dark);
    box-shadow: 0 8px 25px var(--chat-shadow-dark);
}

[data-theme="dark"] .file-preview-info,
[data-theme="dark"] .file-preview-name,
[data-theme="dark"] .recording-indicator {
    color: var(--chat-text-primary-dark);
}

[data-theme="dark"] .file-preview-size {
    color: var(--chat-text-secondary-dark);
}

[data-theme="dark"] .file-preview-icon {
    background: var(--chat-border-dark);
    color: var(--chat-text-secondary-dark);
}

[data-theme="dark"] .recording-cancel {
    background: var(--chat-border-dark);
    color: var(--chat-text-secondary-dark);
}

[data-theme="dark"] .recording-cancel:hover {
    background: var(--chat-input-border-dark);
}

[data-theme="dark"] .emoji-categories {
    border-color: var(--chat-border-dark);
}

[data-theme="dark"] .emoji-category {
    color: var(--chat-text-secondary-dark);
}

[data-theme="dark"] .emoji-category:hover,
[data-theme="dark"] .emoji-category.active {
    background: var(--chat-border-dark);
    color: var(--chat-text-primary-dark);
}

[data-theme="dark"] .emoji-grid::-webkit-scrollbar-track {
    background: var(--chat-border-dark);
}

[data-theme="dark"] .emoji-grid::-webkit-scrollbar-thumb {
    background: var(--chat-text-secondary-dark);
}

[data-theme="dark"] .emoji-item:hover {
    background: var(--chat-border-dark);
}

/* Animations */
@keyframes messageSlideIn {
    from {
        opacity: 0;
        transform: translateY(15px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 0.4;
    }
    50% {
        transform: translate(-50%, -50%) scale(1.5);
        opacity: 0;
    }
    100% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 0.4;
    }
}

@keyframes loadingDots {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes recordingPulse {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.7;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Utility Classes */
.hidden {
    display: none !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .chat-header {
        padding: 0.875rem 1rem;
    }
    
    .chat-messages {
        padding: 1rem;
    }
    
    .message {
        max-width: 85%;
    }
    
    .chat-input-container {
        padding: 0.875rem 1rem;
    }
    
    .input-buttons {
        gap: 0.375rem;
    }
    
    .input-btn,
    .send-btn {
        width: 40px;
        height: 40px;
    }
    
    .emoji-picker {
        width: 280px;
    }
}

@media (max-width: 480px) {
    .control-btn {
        width: 32px;
        height: 32px;
    }
    
    .chat-input-wrapper {
        gap: 0.5rem;
    }
    
    #query-input {
        padding: 0.625rem 0.875rem;
        font-size: 0.9rem;
    }
    
    .input-btn,
    .send-btn {
        width: 36px;
        height: 36px;
    }
    
    .emoji-picker {
        width: 260px;
    }
} 