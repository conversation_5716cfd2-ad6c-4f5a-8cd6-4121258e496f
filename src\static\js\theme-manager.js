/**
 * Advanced Theme Manager for Educational RAG Interface
 * Implements comprehensive dual-theme system with accessibility support
 */

class ThemeManager {
    constructor() {
        this.theme = this.getInitialTheme();
        this.init();
    }

    getInitialTheme() {
        // Check localStorage first
        const savedTheme = localStorage.getItem('theme');
        if (savedTheme && ['light', 'dark'].includes(savedTheme)) {
            return savedTheme;
        }

        // Check system preference
        if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
            return 'dark';
        }

        // Default to light
        return 'light';
    }

    init() {
        console.log('🎨 Initializing Theme Manager...');
        
        // Apply initial theme
        this.applyTheme();
        
        // Create theme toggle button
        this.createThemeToggle();
        
        // Listen for system theme changes
        this.listenForSystemThemeChanges();
        
        // Listen for high contrast preference changes
        this.listenForContrastChanges();
        
        console.log(`✅ Theme Manager initialized with ${this.theme} theme`);
    }

    applyTheme() {
        console.log(`🎨 Applying ${this.theme} theme`);
        
        // Set data attribute on document
        document.documentElement.setAttribute('data-theme', this.theme);
        
        // Update glassmorphism effects based on theme
        if (this.theme === 'dark') {
            document.documentElement.style.setProperty('--backdrop-blur', 'blur(20px)');
        } else {
            document.documentElement.style.setProperty('--backdrop-blur', 'blur(15px)');
        }
        
        // Update meta theme-color for mobile browsers
        this.updateMetaThemeColor();
        
        // Smooth transition effect
        document.body.style.transition = 'background-color 0.3s ease, color 0.3s ease';
        
        // Remove transition after animation
        setTimeout(() => {
            document.body.style.transition = '';
        }, 300);
        
        // Dispatch theme change event
        this.dispatchThemeChangeEvent();
    }

    updateMetaThemeColor() {
        let metaThemeColor = document.querySelector('meta[name="theme-color"]');
        if (!metaThemeColor) {
            metaThemeColor = document.createElement('meta');
            metaThemeColor.name = 'theme-color';
            document.head.appendChild(metaThemeColor);
        }
        
        const themeColors = {
            light: '#ffffff',
            dark: '#0f172a'
        };
        
        metaThemeColor.content = themeColors[this.theme];
    }

    createThemeToggle() {
        // Remove existing toggle if present
        const existingToggle = document.querySelector('.theme-toggle');
        if (existingToggle) {
            existingToggle.remove();
        }

        // Create toggle button
        const toggle = document.createElement('button');
        toggle.className = 'theme-toggle';
        toggle.setAttribute('aria-label', 'Toggle theme');
        toggle.setAttribute('title', 'Toggle between light and dark theme');
        
        // Add icons
        toggle.innerHTML = `
            <span class="theme-icon light-icon">🌙</span>
            <span class="theme-icon dark-icon">☀️</span>
        `;
        
        // Add click handler
        toggle.addEventListener('click', () => this.toggleTheme());
        
        // Add keyboard support
        toggle.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                this.toggleTheme();
            }
        });
        
        // Add to document
        document.body.appendChild(toggle);
        
        console.log('🎛️ Theme toggle button created');
    }

    toggleTheme() {
        console.log(`🔄 Toggling theme from ${this.theme}`);
        
        this.theme = this.theme === 'light' ? 'dark' : 'light';
        localStorage.setItem('theme', this.theme);
        this.applyTheme();
        
        // Update toggle button aria-label
        const toggle = document.querySelector('.theme-toggle');
        if (toggle) {
            toggle.setAttribute('aria-label', `Switch to ${this.theme === 'light' ? 'dark' : 'light'} theme`);
        }
        
        console.log(`✅ Theme switched to ${this.theme}`);
    }

    listenForSystemThemeChanges() {
        if (window.matchMedia) {
            const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
            
            mediaQuery.addEventListener('change', (e) => {
                // Only auto-switch if user hasn't manually set a preference
                if (!localStorage.getItem('theme')) {
                    this.theme = e.matches ? 'dark' : 'light';
                    this.applyTheme();
                    console.log(`🔄 Auto-switched to ${this.theme} theme based on system preference`);
                }
            });
        }
    }

    listenForContrastChanges() {
        if (window.matchMedia) {
            const contrastQuery = window.matchMedia('(prefers-contrast: high)');
            
            contrastQuery.addEventListener('change', (e) => {
                console.log(`🔍 High contrast preference changed: ${e.matches}`);
                // The CSS will handle the high contrast changes automatically
                this.dispatchThemeChangeEvent();
            });
        }
    }

    dispatchThemeChangeEvent() {
        const event = new CustomEvent('themechange', {
            detail: {
                theme: this.theme,
                timestamp: Date.now()
            }
        });
        
        document.dispatchEvent(event);
    }

    // Public API methods
    getCurrentTheme() {
        return this.theme;
    }

    setTheme(theme) {
        if (['light', 'dark'].includes(theme)) {
            this.theme = theme;
            localStorage.setItem('theme', theme);
            this.applyTheme();
            return true;
        }
        return false;
    }

    resetToSystemTheme() {
        localStorage.removeItem('theme');
        this.theme = this.getInitialTheme();
        this.applyTheme();
        console.log('🔄 Reset to system theme preference');
    }
}

// Initialize theme manager when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.themeManager = new ThemeManager();
});

// Also initialize immediately if DOM is already loaded
if (document.readyState === 'loading') {
    // DOM is still loading, wait for DOMContentLoaded
} else {
    // DOM is already loaded
    window.themeManager = new ThemeManager();
}

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ThemeManager;
}
