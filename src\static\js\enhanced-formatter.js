/**
 * Enhanced Educational RAG Formatter
 * Provides ChatGPT-like rich text formatting with educational focus
 */

class EnhancedFormatter {
    constructor() {
        this.initializeMarked();
        this.initializeHighlightJS();
        this.initializeKaTeX();
        this.setupEducationalPatterns();
    }

    initializeMarked() {
        if (typeof marked !== 'undefined') {
            // Configure marked for educational content
            marked.setOptions({
                highlight: (code, lang) => {
                    if (lang && hljs.getLanguage(lang)) {
                        try {
                            return hljs.highlight(code, { language: lang }).value;
                        } catch (err) {
                            console.warn('Highlight.js error:', err);
                        }
                    }
                    return hljs.highlightAuto(code).value;
                },
                breaks: true,
                gfm: true,
                sanitize: false,
                smartLists: true,
                smartypants: true
            });

            // Custom renderer for educational content
            const renderer = new marked.Renderer();
            
            // Enhanced heading renderer
            renderer.heading = (text, level) => {
                const escapedText = text.toLowerCase().replace(/[^\w]+/g, '-');
                const className = `heading-level-${level}`;
                return `<h${level} id="${escapedText}" class="${className}">${text}</h${level}>`;
            };

            // Enhanced paragraph renderer
            renderer.paragraph = (text) => {
                // Detect and format educational patterns
                text = this.formatEducationalPatterns(text);
                return `<p>${text}</p>`;
            };

            // Enhanced list renderer
            renderer.list = (body, ordered, start) => {
                const type = ordered ? 'ol' : 'ul';
                const startatt = (ordered && start !== 1) ? ` start="${start}"` : '';
                return `<${type}${startatt} class="enhanced-list">${body}</${type}>`;
            };

            // Enhanced blockquote renderer
            renderer.blockquote = (quote) => {
                return `<blockquote class="educational-quote">${quote}</blockquote>`;
            };

            // Enhanced table renderer
            renderer.table = (header, body) => {
                return `<div class="table-wrapper">
                    <table class="educational-table">
                        <thead>${header}</thead>
                        <tbody>${body}</tbody>
                    </table>
                </div>`;
            };

            marked.use({ renderer });
        }
    }

    initializeHighlightJS() {
        if (typeof hljs !== 'undefined') {
            hljs.configure({
                classPrefix: 'hljs-',
                languages: ['javascript', 'python', 'html', 'css', 'json', 'markdown']
            });
        }
    }

    initializeKaTeX() {
        if (typeof katex !== 'undefined' && typeof renderMathInElement !== 'undefined') {
            this.katexOptions = {
                delimiters: [
                    { left: '$$', right: '$$', display: true },
                    { left: '$', right: '$', display: false },
                    { left: '\\[', right: '\\]', display: true },
                    { left: '\\(', right: '\\)', display: false }
                ],
                throwOnError: false,
                errorColor: '#cc0000',
                strict: 'warn'
            };
        }
    }

    setupEducationalPatterns() {
        // Educational content patterns for enhanced formatting
        this.patterns = {
            // Dates (e.g., "1789", "15th August 1947")
            dates: /\b(\d{1,2}(?:st|nd|rd|th)?\s+(?:January|February|March|April|May|June|July|August|September|October|November|December)\s+\d{4}|\d{4})\b/gi,
            
            // Proper nouns (capitalized words)
            properNouns: /\b[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*\b/g,
            
            // Scientific terms (words ending in -ism, -tion, -sion, etc.)
            scientificTerms: /\b\w+(?:ism|tion|sion|ology|graphy|metry)\b/gi,
            
            // Important concepts (words in quotes)
            concepts: /"([^"]+)"/g,
            
            // Numbers with units
            measurements: /\b\d+(?:\.\d+)?\s*(?:km|m|cm|mm|kg|g|°C|°F|%|years?|centuries?)\b/gi,
            
            // Subject-specific terms
            subjects: {
                history: /\b(?:revolution|empire|dynasty|civilization|war|battle|treaty|independence|freedom|struggle)\b/gi,
                geography: /\b(?:mountain|river|plateau|climate|monsoon|vegetation|forest|desert|ocean|continent)\b/gi,
                economics: /\b(?:economy|GDP|inflation|market|trade|industry|agriculture|development|poverty|employment)\b/gi,
                politics: /\b(?:democracy|government|constitution|parliament|election|rights|citizenship|federalism|judiciary)\b/gi
            }
        };
    }

    formatEducationalPatterns(text) {
        // Format dates
        text = text.replace(this.patterns.dates, '<span class="edu-date">$1</span>');
        
        // Format measurements
        text = text.replace(this.patterns.measurements, '<span class="edu-measurement">$1</span>');
        
        // Format concepts in quotes
        text = text.replace(this.patterns.concepts, '<span class="edu-concept">"$1"</span>');
        
        // Format scientific terms
        text = text.replace(this.patterns.scientificTerms, '<em class="edu-scientific">$1</em>');
        
        return text;
    }

    detectSubject(content) {
        const subjects = ['history', 'geography', 'economics', 'politics'];
        const scores = {};
        
        subjects.forEach(subject => {
            const pattern = this.patterns.subjects[subject];
            const matches = content.match(pattern) || [];
            scores[subject] = matches.length;
        });
        
        // Return subject with highest score
        return Object.keys(scores).reduce((a, b) => scores[a] > scores[b] ? a : b);
    }

    formatResponse(rawResponse, options = {}) {
        try {
            // Detect subject for styling
            const subject = this.detectSubject(rawResponse);
            
            // Process markdown
            let formattedContent = marked.parse(rawResponse);
            
            // Apply educational formatting
            formattedContent = this.applyEducationalFormatting(formattedContent, subject);
            
            // Create enhanced message container
            const messageContainer = this.createMessageContainer(formattedContent, subject, options);
            
            // Process mathematical expressions
            this.processMathExpressions(messageContainer);
            
            // Add interactive elements
            this.addInteractiveElements(messageContainer);
            
            return messageContainer;
            
        } catch (error) {
            console.error('Formatting error:', error);
            return this.createFallbackMessage(rawResponse);
        }
    }

    applyEducationalFormatting(content, subject) {
        // Apply subject-specific formatting
        Object.keys(this.patterns.subjects).forEach(subj => {
            if (subj === subject) {
                const pattern = this.patterns.subjects[subj];
                content = content.replace(pattern, `<strong class="subject-term subject-${subj}">$1</strong>`);
            }
        });
        
        return content;
    }

    createMessageContainer(content, subject, options) {
        const container = document.createElement('div');
        container.className = `enhanced-message subject-${subject}`;
        
        const messageContent = document.createElement('div');
        messageContent.className = 'enhanced-message-content';
        messageContent.innerHTML = content;
        
        // Add metadata
        if (options.showMetadata) {
            const metadata = this.createMetadata(subject, options);
            messageContent.appendChild(metadata);
        }
        
        container.appendChild(messageContent);
        return container;
    }

    createMetadata(subject, options) {
        const metadata = document.createElement('div');
        metadata.className = 'message-metadata';
        
        const subjectBadge = document.createElement('span');
        subjectBadge.className = `subject-badge subject-${subject}`;
        subjectBadge.textContent = subject.charAt(0).toUpperCase() + subject.slice(1);
        
        metadata.appendChild(subjectBadge);
        
        if (options.confidence) {
            const confidence = document.createElement('span');
            confidence.className = 'confidence-score';
            confidence.textContent = `${(options.confidence * 100).toFixed(1)}% confidence`;
            metadata.appendChild(confidence);
        }
        
        if (options.processingTime) {
            const timing = document.createElement('span');
            timing.className = 'processing-time';
            timing.textContent = `${options.processingTime.toFixed(2)}s`;
            metadata.appendChild(timing);
        }
        
        return metadata;
    }

    processMathExpressions(container) {
        if (typeof renderMathInElement !== 'undefined') {
            try {
                renderMathInElement(container, this.katexOptions);
            } catch (error) {
                console.warn('KaTeX rendering error:', error);
            }
        }
    }

    addInteractiveElements(container) {
        // Add collapsible sections
        const headings = container.querySelectorAll('h2, h3');
        headings.forEach((heading, index) => {
            if (heading.nextElementSibling) {
                this.makeCollapsible(heading, index);
            }
        });
        
        // Add copy buttons to code blocks
        const codeBlocks = container.querySelectorAll('pre code');
        codeBlocks.forEach(block => {
            this.addCopyButton(block.parentElement);
        });
        
        // Add hover effects to tables
        const tables = container.querySelectorAll('table');
        tables.forEach(table => {
            this.enhanceTable(table);
        });
    }

    makeCollapsible(heading, index) {
        const content = [];
        let nextElement = heading.nextElementSibling;
        
        // Collect content until next heading of same or higher level
        while (nextElement && !this.isHeadingOfSameOrHigherLevel(nextElement, heading)) {
            content.push(nextElement);
            nextElement = nextElement.nextElementSibling;
        }
        
        if (content.length > 0) {
            // Create collapsible structure
            const section = document.createElement('div');
            section.className = 'collapsible-section';
            
            const header = document.createElement('div');
            header.className = 'collapsible-header';
            header.innerHTML = `
                ${heading.innerHTML}
                <i class="fas fa-chevron-down collapsible-icon"></i>
            `;
            
            const contentDiv = document.createElement('div');
            contentDiv.className = 'collapsible-content';
            
            // Move content into collapsible div
            content.forEach(element => {
                contentDiv.appendChild(element);
            });
            
            section.appendChild(header);
            section.appendChild(contentDiv);
            
            // Replace heading with collapsible section
            heading.parentNode.replaceChild(section, heading);
            
            // Add click handler
            header.addEventListener('click', () => {
                section.classList.toggle('expanded');
                contentDiv.classList.toggle('active');
            });
        }
    }

    isHeadingOfSameOrHigherLevel(element, referenceHeading) {
        if (!element.tagName || !element.tagName.match(/^H[1-6]$/)) {
            return false;
        }
        
        const elementLevel = parseInt(element.tagName.charAt(1));
        const referenceLevel = parseInt(referenceHeading.tagName.charAt(1));
        
        return elementLevel <= referenceLevel;
    }

    addCopyButton(codeBlock) {
        const button = document.createElement('button');
        button.className = 'copy-code-btn';
        button.innerHTML = '<i class="fas fa-copy"></i>';
        button.title = 'Copy code';
        
        button.addEventListener('click', () => {
            const code = codeBlock.querySelector('code').textContent;
            navigator.clipboard.writeText(code).then(() => {
                button.innerHTML = '<i class="fas fa-check"></i>';
                setTimeout(() => {
                    button.innerHTML = '<i class="fas fa-copy"></i>';
                }, 2000);
            });
        });
        
        codeBlock.style.position = 'relative';
        codeBlock.appendChild(button);
    }

    enhanceTable(table) {
        // Add sorting capability to headers
        const headers = table.querySelectorAll('th');
        headers.forEach((header, index) => {
            header.style.cursor = 'pointer';
            header.addEventListener('click', () => {
                this.sortTable(table, index);
            });
        });
    }

    sortTable(table, columnIndex) {
        const tbody = table.querySelector('tbody');
        const rows = Array.from(tbody.querySelectorAll('tr'));
        
        rows.sort((a, b) => {
            const aText = a.cells[columnIndex].textContent.trim();
            const bText = b.cells[columnIndex].textContent.trim();
            
            // Try numeric sort first
            const aNum = parseFloat(aText);
            const bNum = parseFloat(bText);
            
            if (!isNaN(aNum) && !isNaN(bNum)) {
                return aNum - bNum;
            }
            
            // Fallback to string sort
            return aText.localeCompare(bText);
        });
        
        // Re-append sorted rows
        rows.forEach(row => tbody.appendChild(row));
    }

    createFallbackMessage(content) {
        const container = document.createElement('div');
        container.className = 'enhanced-message fallback';
        
        const messageContent = document.createElement('div');
        messageContent.className = 'enhanced-message-content';
        messageContent.innerHTML = `<p>${this.escapeHtml(content)}</p>`;
        
        container.appendChild(messageContent);
        return container;
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // Public API methods
    formatMessage(content, options = {}) {
        return this.formatResponse(content, options);
    }

    updateTheme(theme) {
        document.documentElement.setAttribute('data-theme', theme);
    }

    getFormattingStats(content) {
        return {
            wordCount: content.split(/\s+/).length,
            characterCount: content.length,
            subject: this.detectSubject(content),
            hasMarkdown: /[*_`#\[\]]/g.test(content),
            hasMath: /\$|\\\(|\\\[/g.test(content),
            hasCode: /```|`/g.test(content)
        };
    }
}

// Initialize global formatter instance
window.enhancedFormatter = new EnhancedFormatter();

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = EnhancedFormatter;
}
