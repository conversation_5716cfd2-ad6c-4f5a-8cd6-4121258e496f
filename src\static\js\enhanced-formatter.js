/**
 * Enhanced Educational RAG Formatter
 * Provides ChatGPT-like rich text formatting with educational focus
 */

class EnhancedFormatter {
    constructor() {
        this.initializeMarked();
        this.initializeHighlightJS();
        this.initializeKaTeX();
        this.setupEducationalPatterns();
    }

    initializeMarked() {
        // Wait for marked to be available
        this.markedReady = typeof marked !== 'undefined';

        if (this.markedReady) {
            console.log('✅ Marked.js initialized for enhanced formatting');
            // Configure marked for educational content
            marked.setOptions({
                highlight: (code, lang) => {
                    if (typeof hljs !== 'undefined' && lang && hljs.getLanguage(lang)) {
                        try {
                            return hljs.highlight(code, { language: lang }).value;
                        } catch (err) {
                            console.warn('Highlight.js error:', err);
                        }
                    }
                    if (typeof hljs !== 'undefined') {
                        return hljs.highlightAuto(code).value;
                    }
                    return code;
                },
                breaks: true,
                gfm: true,
                sanitize: false,
                smartLists: true,
                smartypants: true
            });
        } else {
            console.warn('⚠️ Marked.js not available, using fallback formatting');
        }

            // Custom renderer for educational content
            const renderer = new marked.Renderer();
            
            // Enhanced heading renderer
            renderer.heading = (text, level) => {
                const escapedText = text.toLowerCase().replace(/[^\w]+/g, '-');
                const className = `heading-level-${level}`;
                return `<h${level} id="${escapedText}" class="${className}">${text}</h${level}>`;
            };

            // Enhanced paragraph renderer
            renderer.paragraph = (text) => {
                // Detect and format educational patterns
                text = this.formatEducationalPatterns(text);
                return `<p>${text}</p>`;
            };

            // Enhanced list renderer
            renderer.list = (body, ordered, start) => {
                const type = ordered ? 'ol' : 'ul';
                const startatt = (ordered && start !== 1) ? ` start="${start}"` : '';
                return `<${type}${startatt} class="enhanced-list">${body}</${type}>`;
            };

            // Enhanced blockquote renderer
            renderer.blockquote = (quote) => {
                return `<blockquote class="educational-quote">${quote}</blockquote>`;
            };

            // Enhanced table renderer
            renderer.table = (header, body) => {
                return `<div class="table-wrapper">
                    <table class="educational-table">
                        <thead>${header}</thead>
                        <tbody>${body}</tbody>
                    </table>
                </div>`;
            };

            marked.use({ renderer });
        }
    }

    initializeHighlightJS() {
        if (typeof hljs !== 'undefined') {
            hljs.configure({
                classPrefix: 'hljs-',
                languages: ['javascript', 'python', 'html', 'css', 'json', 'markdown']
            });
        }
    }

    initializeKaTeX() {
        if (typeof katex !== 'undefined' && typeof renderMathInElement !== 'undefined') {
            this.katexOptions = {
                delimiters: [
                    { left: '$$', right: '$$', display: true },
                    { left: '$', right: '$', display: false },
                    { left: '\\[', right: '\\]', display: true },
                    { left: '\\(', right: '\\)', display: false }
                ],
                throwOnError: false,
                errorColor: '#cc0000',
                strict: 'warn'
            };
        }
    }

    setupEducationalPatterns() {
        // Enhanced educational content patterns for professional formatting
        this.patterns = {
            // Professional heading patterns
            mainHeadings: /\*\*([A-Z\s]+)\*\*/g, // **BOLD ALL CAPS**
            subHeadings: /\*\*([A-Z][a-z\s]+)\*\*/g, // **Bold Title Case**
            sectionHeaders: /\*\*\*([^*]+)\*\*\*/g, // ***Bold Italic***

            // Academic citations
            citations: /\[Page\s+(\d+)\]/gi,

            // Dates with enhanced formatting
            dates: /\b(\d{1,2}(?:st|nd|rd|th)?\s+(?:January|February|March|April|May|June|July|August|September|October|November|December)\s+\d{4}|\*\*\*\d{4}\s+CE\*\*\*|\*\*\d{1,2}(?:st|nd|rd|th)?\s+[A-Z][a-z]+\s+\d{4}\*\*)\b/gi,

            // Enhanced proper nouns (should be italicized)
            properNouns: /\b[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*\b/g,

            // Scientific and academic terms
            scientificTerms: /\b\w+(?:ism|tion|sion|ology|graphy|metry|acy|ity|ment)\b/gi,

            // Important concepts and definitions
            concepts: /"([^"]+)"/g,
            blockquotes: />\s*\*\*([^*]+)\*\*:\s*([^\n]+)/g,

            // Numbers with units and measurements
            measurements: /\b\d+(?:\.\d+)?\s*(?:km|m|cm|mm|kg|g|°C|°F|%|years?|centuries?|crores?|lakhs?)\b/gi,

            // Visual elements patterns
            asciiDiagrams: /```[\s\S]*?```/g,
            tables: /\|[^|\n]*\|[\s\S]*?\|[^|\n]*\|/g,
            flowcharts: /→|↓|↑|←/g,

            // Summary points and checkmarks
            summaryPoints: /✓\s*([^\n]+)/g,

            // Subject-specific enhanced terms
            subjects: {
                history: /\b(?:revolution|empire|dynasty|civilization|war|battle|treaty|independence|freedom|struggle|colonialism|nationalism|feudalism)\b/gi,
                geography: /\b(?:mountain|river|plateau|climate|monsoon|vegetation|forest|desert|ocean|continent|latitude|longitude|topography)\b/gi,
                economics: /\b(?:economy|GDP|inflation|market|trade|industry|agriculture|development|poverty|employment|capitalism|socialism)\b/gi,
                politics: /\b(?:democracy|government|constitution|parliament|election|rights|citizenship|federalism|judiciary|sovereignty|republic)\b/gi
            }
        };

        // Professional formatting rules
        this.formattingRules = {
            enforceItalicsForProperNouns: true,
            enhanceCitations: true,
            createInteractiveTables: true,
            generateVisualElements: true,
            validateAcademicStructure: true
        };
    }

    formatEducationalPatterns(text) {
        // Format dates
        text = text.replace(this.patterns.dates, (match, p1) => `<span class="edu-date">${p1}</span>`);

        // Format measurements
        text = text.replace(this.patterns.measurements, (match) => `<span class="edu-measurement">${match}</span>`);

        // Format concepts in quotes
        text = text.replace(this.patterns.concepts, (match, p1) => `<span class="edu-concept">"${p1}"</span>`);

        // Format scientific terms
        text = text.replace(this.patterns.scientificTerms, (match) => `<em class="edu-scientific">${match}</em>`);

        return text;
    }

    detectSubject(content) {
        const subjects = ['history', 'geography', 'economics', 'politics'];
        const scores = {};
        
        subjects.forEach(subject => {
            const pattern = this.patterns.subjects[subject];
            const matches = content.match(pattern) || [];
            scores[subject] = matches.length;
        });
        
        // Return subject with highest score
        return Object.keys(scores).reduce((a, b) => scores[a] > scores[b] ? a : b);
    }

    formatResponse(rawResponse, options = {}) {
        try {
            console.log('🎨 Starting enhanced formatting for response...');

            // Detect subject for styling
            const subject = this.detectSubject(rawResponse);
            console.log(`📚 Detected subject: ${subject}`);

            // Apply professional pre-processing
            let preprocessedContent = this.applyProfessionalPreprocessing(rawResponse);
            console.log('✅ Professional preprocessing applied');

            // Validate and enhance academic structure
            preprocessedContent = this.validateAcademicStructure(preprocessedContent);

            // Process markdown with enhanced rules
            let formattedContent;
            if (this.markedReady && typeof marked !== 'undefined') {
                console.log('📝 Processing markdown with marked.js');
                formattedContent = marked.parse(preprocessedContent);
            } else {
                console.log('📝 Using fallback markdown processing');
                formattedContent = this.processMarkdownFallback(preprocessedContent);
            }

            // Apply professional educational formatting
            formattedContent = this.applyProfessionalFormatting(formattedContent, subject);
            console.log('✅ Professional formatting applied');

            // Enhance citations and references
            formattedContent = this.enhanceCitations(formattedContent);

            // Create professional message container
            const messageContainer = this.createProfessionalMessageContainer(formattedContent, subject, options);

            // Process mathematical expressions
            this.processMathExpressions(messageContainer);

            // Add interactive and visual elements
            this.addProfessionalInteractiveElements(messageContainer);

            // Validate final formatting quality
            this.validateFormattingQuality(messageContainer);

            console.log('🎉 Enhanced formatting completed successfully');
            return messageContainer;

        } catch (error) {
            console.error('❌ Professional formatting error:', error);
            return this.createFallbackMessage(rawResponse);
        }
    }

    applyProfessionalPreprocessing(content) {
        console.log('🔧 Applying professional preprocessing...');

        // First, ensure we have proper markdown structure
        content = this.ensureMarkdownStructure(content);

        // Ensure proper heading hierarchy
        content = this.enforceHeadingHierarchy(content);

        // Enhance proper noun formatting
        if (this.formattingRules.enforceItalicsForProperNouns) {
            content = this.enforceProperNounItalics(content);
        }

        // Standardize citation format
        content = this.standardizeCitations(content);

        // Enhance visual elements
        content = this.enhanceVisualElements(content);

        console.log('✅ Professional preprocessing completed');
        return content;
    }

    ensureMarkdownStructure(content) {
        // If content doesn't have proper markdown structure, add it
        let structured = content;

        // Detect if content has headings, if not, create structure
        if (!structured.includes('##') && !structured.includes('**')) {
            console.log('📝 Adding markdown structure to plain text response');

            // Split content into paragraphs
            const paragraphs = structured.split('\n\n').filter(p => p.trim());

            if (paragraphs.length > 1) {
                // First paragraph as main heading
                structured = `## **${paragraphs[0].trim()}**\n\n`;

                // Process remaining paragraphs
                for (let i = 1; i < paragraphs.length; i++) {
                    const para = paragraphs[i].trim();

                    // If paragraph looks like a heading (short, no punctuation at end)
                    if (para.length < 100 && !para.endsWith('.') && !para.endsWith('!') && !para.endsWith('?')) {
                        structured += `### **${para}**\n\n`;
                    } else {
                        // Regular paragraph with enhanced formatting
                        let enhancedPara = para;

                        // Bold key terms at start of sentences
                        enhancedPara = enhancedPara.replace(/^([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)/gm, '**$1**');

                        structured += `${enhancedPara}\n\n`;
                    }
                }
            }
        }

        return structured;
    }

    enforceHeadingHierarchy(content) {
        // Ensure main headings are **BOLD ALL CAPS**
        content = content.replace(/^##\s*([A-Z\s]+)$/gm, (match, p1) => `## **${p1}**`);

        // Ensure subheadings are **Bold Title Case**
        content = content.replace(/^###\s*([A-Z][a-z\s]+)$/gm, (match, p1) => `### **${p1}**`);

        // Ensure section headers are ***Bold Italic***
        content = content.replace(/^####\s*([^#]+)$/gm, (match, p1) => `#### ***${p1}***`);

        return content;
    }

    enforceProperNounItalics(content) {
        // Enhanced proper noun detection and italicization
        const properNounPatterns = [
            // Historical figures and places
            /\b(Gandhi|Nehru|Churchill|Hitler|Stalin|Roosevelt|Delhi|Mumbai|Kolkata|Chennai|London|Paris|Berlin|Moscow|Washington)\b/g,
            // Countries and regions
            /\b(India|Pakistan|Bangladesh|China|Britain|France|Germany|Russia|America|Europe|Asia|Africa)\b/g,
            // Historical events and periods
            /\b(World War|French Revolution|Industrial Revolution|Renaissance|Mughal Empire|British Raj)\b/g,
            // Institutions and organizations
            /\b(Parliament|Congress|United Nations|World Bank|Supreme Court|High Court)\b/g
        ];

        // Only apply if not already italicized
        properNounPatterns.forEach(pattern => {
            content = content.replace(pattern, (match, p1) => {
                // Check if already italicized
                if (content.includes(`*${p1}*`)) {
                    return match;
                }
                return `*${p1}*`;
            });
        });

        return content;
    }

    standardizeCitations(content) {
        // Standardize citation format to [Page X]
        content = content.replace(/\(Page\s*(\d+)\)/gi, (match, p1) => `[Page ${p1}]`);
        content = content.replace(/\(p\.\s*(\d+)\)/gi, (match, p1) => `[Page ${p1}]`);
        content = content.replace(/\(pg\.\s*(\d+)\)/gi, (match, p1) => `[Page ${p1}]`);

        return content;
    }

    enhanceVisualElements(content) {
        // Enhance ASCII diagrams
        content = content.replace(/```([\s\S]*?)```/g, (match, diagram) => {
            if (diagram.includes('→') || diagram.includes('↓') || diagram.includes('|')) {
                return `\`\`\`diagram\n${diagram.trim()}\n\`\`\``;
            }
            return match;
        });

        // Enhance tables
        content = content.replace(/(\|[^|\n]*\|[\s\S]*?\|[^|\n]*\|)/g, (match) => {
            return `<div class="professional-table-wrapper">\n${match}\n</div>`;
        });

        return content;
    }

    processMarkdownFallback(content) {
        console.log('📝 Using enhanced fallback markdown processing');

        // Enhanced markdown processing without marked.js
        let processed = content;

        // Process headings with proper hierarchy
        processed = processed.replace(/^### (.*$)/gm, '<h3 class="enhanced-h3">$1</h3>');
        processed = processed.replace(/^## (.*$)/gm, '<h2 class="enhanced-h2">$1</h2>');
        processed = processed.replace(/^# (.*$)/gm, '<h1 class="enhanced-h1">$1</h1>');

        // Process bold and italic
        processed = processed.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
        processed = processed.replace(/\*(.*?)\*/g, '<em>$1</em>');

        // Process blockquotes
        processed = processed.replace(/^> (.*$)/gm, '<blockquote class="enhanced-blockquote">$1</blockquote>');

        // Process code blocks
        processed = processed.replace(/```([\s\S]*?)```/g, '<pre class="enhanced-code"><code>$1</code></pre>');
        processed = processed.replace(/`(.*?)`/g, '<code class="enhanced-inline-code">$1</code>');

        // Process lists
        processed = processed.replace(/^\d+\.\s+(.*$)/gm, '<li class="enhanced-ol-item">$1</li>');
        processed = processed.replace(/^[-*]\s+(.*$)/gm, '<li class="enhanced-ul-item">$1</li>');

        // Wrap consecutive list items
        processed = processed.replace(/(<li class="enhanced-ol-item">.*?<\/li>)/gs, '<ol class="enhanced-ol">$1</ol>');
        processed = processed.replace(/(<li class="enhanced-ul-item">.*?<\/li>)/gs, '<ul class="enhanced-ul">$1</ul>');

        // Process line breaks
        processed = processed.replace(/\n\n/g, '</p><p class="enhanced-paragraph">');
        processed = processed.replace(/\n/g, '<br>');

        // Wrap in paragraphs
        if (!processed.includes('<h1>') && !processed.includes('<h2>') && !processed.includes('<h3>')) {
            processed = `<p class="enhanced-paragraph">${processed}</p>`;
        }

        return processed;
    }

    validateAcademicStructure(content) {
        // Ensure proper academic structure
        const requiredSections = ['Overview', 'Core Concepts', 'Detailed Explanation', 'Summary'];
        let structureScore = 0;

        requiredSections.forEach(section => {
            if (content.includes(section)) {
                structureScore++;
            }
        });

        // If structure is incomplete, add guidance
        if (structureScore < 3) {
            console.warn('Academic structure validation: Some required sections may be missing');
        }

        return content;
    }

    applyProfessionalFormatting(content, subject) {
        // Apply enhanced subject-specific formatting
        Object.keys(this.patterns.subjects).forEach(subj => {
            if (subj === subject) {
                const pattern = this.patterns.subjects[subj];
                content = content.replace(pattern, (match) => `<strong class="professional-term subject-${subj}">${match}</strong>`);
            }
        });

        // Enhance blockquotes for definitions
        content = content.replace(this.patterns.blockquotes, (match, p1, p2) =>
            `<blockquote class="professional-definition"><strong>${p1}</strong>: ${p2}</blockquote>`);

        // Enhance summary points
        content = content.replace(this.patterns.summaryPoints, (match, p1) =>
            `<div class="summary-point"><i class="fas fa-check-circle"></i> ${p1}</div>`);

        // Enhance measurements and statistics
        content = content.replace(this.patterns.measurements, (match) =>
            `<span class="measurement-highlight">${match}</span>`);

        return content;
    }

    enhanceCitations(content) {
        if (!this.formattingRules.enhanceCitations) return content;

        // Enhance citation formatting
        content = content.replace(this.patterns.citations, (match, p1) =>
            `<span class="academic-citation" title="Source Reference">[Page ${p1}]</span>`);

        // Add citation hover functionality
        content = content.replace(/\[Page\s+(\d+)\]/gi, (match, p1) =>
            `<span class="enhanced-citation" data-page="${p1}" title="Click to view source details">[Page ${p1}]</span>`);

        return content;
    }

    createProfessionalMessageContainer(content, subject, options) {
        const container = document.createElement('div');
        container.className = `professional-message enhanced-message subject-${subject}`;

        // Add professional header
        const header = this.createProfessionalHeader(subject, options);
        if (header) {
            container.appendChild(header);
        }

        const messageContent = document.createElement('div');
        messageContent.className = 'professional-message-content enhanced-message-content';
        messageContent.innerHTML = content;

        // Add professional metadata
        if (options.showMetadata) {
            const metadata = this.createProfessionalMetadata(subject, options);
            messageContent.appendChild(metadata);
        }

        container.appendChild(messageContent);
        return container;
    }

    createProfessionalHeader(subject, options) {
        if (!options.showHeader) return null;

        const header = document.createElement('div');
        header.className = 'professional-message-header';

        const subjectIcon = this.getSubjectIcon(subject);
        const subjectName = subject.charAt(0).toUpperCase() + subject.slice(1);

        header.innerHTML = `
            <div class="subject-indicator">
                <i class="${subjectIcon}"></i>
                <span class="subject-name">${subjectName}</span>
            </div>
            <div class="response-quality">
                <span class="quality-badge professional">Professional Response</span>
            </div>
        `;

        return header;
    }

    getSubjectIcon(subject) {
        const icons = {
            history: 'fas fa-scroll',
            geography: 'fas fa-globe-americas',
            economics: 'fas fa-chart-line',
            politics: 'fas fa-landmark',
            general: 'fas fa-book'
        };
        return icons[subject] || icons.general;
    }

    createProfessionalMetadata(subject, options) {
        const metadata = document.createElement('div');
        metadata.className = 'professional-metadata message-metadata';

        const elements = [];

        // Subject badge with enhanced styling
        if (subject) {
            elements.push(`<span class="professional-subject-badge subject-badge subject-${subject}">${subject.toUpperCase()}</span>`);
        }

        // Confidence score with visual indicator
        if (options.confidence) {
            const confidenceLevel = this.getConfidenceLevel(options.confidence);
            elements.push(`<span class="professional-confidence confidence-score ${confidenceLevel}">
                <i class="fas fa-chart-bar"></i> ${(options.confidence * 100).toFixed(1)}% confidence
            </span>`);
        }

        // Processing time with performance indicator
        if (options.processingTime) {
            const performanceLevel = this.getPerformanceLevel(options.processingTime);
            elements.push(`<span class="professional-timing processing-time ${performanceLevel}">
                <i class="fas fa-clock"></i> ${options.processingTime.toFixed(2)}s
            </span>`);
        }

        // Academic quality indicator
        elements.push(`<span class="academic-quality">
            <i class="fas fa-graduation-cap"></i> CBSE Standard
        </span>`);

        metadata.innerHTML = elements.join('');
        return metadata;
    }

    getConfidenceLevel(confidence) {
        if (confidence >= 0.8) return 'high-confidence';
        if (confidence >= 0.6) return 'medium-confidence';
        return 'low-confidence';
    }

    getPerformanceLevel(time) {
        if (time <= 2) return 'fast-response';
        if (time <= 5) return 'normal-response';
        return 'slow-response';
    }

    createMessageContainer(content, subject, options) {
        const container = document.createElement('div');
        container.className = `enhanced-message subject-${subject}`;
        
        const messageContent = document.createElement('div');
        messageContent.className = 'enhanced-message-content';
        messageContent.innerHTML = content;
        
        // Add metadata
        if (options.showMetadata) {
            const metadata = this.createMetadata(subject, options);
            messageContent.appendChild(metadata);
        }
        
        container.appendChild(messageContent);
        return container;
    }

    createMetadata(subject, options) {
        const metadata = document.createElement('div');
        metadata.className = 'message-metadata';
        
        const subjectBadge = document.createElement('span');
        subjectBadge.className = `subject-badge subject-${subject}`;
        subjectBadge.textContent = subject.charAt(0).toUpperCase() + subject.slice(1);
        
        metadata.appendChild(subjectBadge);
        
        if (options.confidence) {
            const confidence = document.createElement('span');
            confidence.className = 'confidence-score';
            confidence.textContent = `${(options.confidence * 100).toFixed(1)}% confidence`;
            metadata.appendChild(confidence);
        }
        
        if (options.processingTime) {
            const timing = document.createElement('span');
            timing.className = 'processing-time';
            timing.textContent = `${options.processingTime.toFixed(2)}s`;
            metadata.appendChild(timing);
        }
        
        return metadata;
    }

    processMathExpressions(container) {
        if (typeof renderMathInElement !== 'undefined') {
            try {
                renderMathInElement(container, this.katexOptions);
            } catch (error) {
                console.warn('KaTeX rendering error:', error);
            }
        }
    }

    addInteractiveElements(container) {
        // Add collapsible sections
        const headings = container.querySelectorAll('h2, h3');
        headings.forEach((heading, index) => {
            if (heading.nextElementSibling) {
                this.makeCollapsible(heading, index);
            }
        });
        
        // Add copy buttons to code blocks
        const codeBlocks = container.querySelectorAll('pre code');
        codeBlocks.forEach(block => {
            this.addCopyButton(block.parentElement);
        });
        
        // Add hover effects to tables
        const tables = container.querySelectorAll('table');
        tables.forEach(table => {
            this.enhanceTable(table);
        });
    }

    makeCollapsible(heading, index) {
        const content = [];
        let nextElement = heading.nextElementSibling;
        
        // Collect content until next heading of same or higher level
        while (nextElement && !this.isHeadingOfSameOrHigherLevel(nextElement, heading)) {
            content.push(nextElement);
            nextElement = nextElement.nextElementSibling;
        }
        
        if (content.length > 0) {
            // Create collapsible structure
            const section = document.createElement('div');
            section.className = 'collapsible-section';
            
            const header = document.createElement('div');
            header.className = 'collapsible-header';
            header.innerHTML = `
                ${heading.innerHTML}
                <i class="fas fa-chevron-down collapsible-icon"></i>
            `;
            
            const contentDiv = document.createElement('div');
            contentDiv.className = 'collapsible-content';
            
            // Move content into collapsible div
            content.forEach(element => {
                contentDiv.appendChild(element);
            });
            
            section.appendChild(header);
            section.appendChild(contentDiv);
            
            // Replace heading with collapsible section
            heading.parentNode.replaceChild(section, heading);
            
            // Add click handler
            header.addEventListener('click', () => {
                section.classList.toggle('expanded');
                contentDiv.classList.toggle('active');
            });
        }
    }

    isHeadingOfSameOrHigherLevel(element, referenceHeading) {
        if (!element.tagName || !element.tagName.match(/^H[1-6]$/)) {
            return false;
        }
        
        const elementLevel = parseInt(element.tagName.charAt(1));
        const referenceLevel = parseInt(referenceHeading.tagName.charAt(1));
        
        return elementLevel <= referenceLevel;
    }

    addCopyButton(codeBlock) {
        const button = document.createElement('button');
        button.className = 'copy-code-btn';
        button.innerHTML = '<i class="fas fa-copy"></i>';
        button.title = 'Copy code';
        
        button.addEventListener('click', () => {
            const code = codeBlock.querySelector('code').textContent;
            navigator.clipboard.writeText(code).then(() => {
                button.innerHTML = '<i class="fas fa-check"></i>';
                setTimeout(() => {
                    button.innerHTML = '<i class="fas fa-copy"></i>';
                }, 2000);
            });
        });
        
        codeBlock.style.position = 'relative';
        codeBlock.appendChild(button);
    }

    enhanceTable(table) {
        // Add sorting capability to headers
        const headers = table.querySelectorAll('th');
        headers.forEach((header, index) => {
            header.style.cursor = 'pointer';
            header.addEventListener('click', () => {
                this.sortTable(table, index);
            });
        });
    }

    sortTable(table, columnIndex) {
        const tbody = table.querySelector('tbody');
        const rows = Array.from(tbody.querySelectorAll('tr'));
        
        rows.sort((a, b) => {
            const aText = a.cells[columnIndex].textContent.trim();
            const bText = b.cells[columnIndex].textContent.trim();
            
            // Try numeric sort first
            const aNum = parseFloat(aText);
            const bNum = parseFloat(bText);
            
            if (!isNaN(aNum) && !isNaN(bNum)) {
                return aNum - bNum;
            }
            
            // Fallback to string sort
            return aText.localeCompare(bText);
        });
        
        // Re-append sorted rows
        rows.forEach(row => tbody.appendChild(row));
    }

    addProfessionalInteractiveElements(container) {
        // Add enhanced collapsible sections
        this.addProfessionalCollapsibleSections(container);

        // Add interactive citations
        this.addInteractiveCitations(container);

        // Add professional copy buttons to code blocks
        this.addProfessionalCopyButtons(container);

        // Add enhanced table interactions
        this.addProfessionalTableInteractions(container);

        // Add visual diagram interactions
        this.addVisualDiagramInteractions(container);
    }

    addProfessionalCollapsibleSections(container) {
        const headings = container.querySelectorAll('h2, h3, h4');
        headings.forEach((heading, index) => {
            if (heading.nextElementSibling && this.shouldMakeCollapsible(heading)) {
                this.makeProfessionalCollapsible(heading, index);
            }
        });
    }

    shouldMakeCollapsible(heading) {
        const collapsibleSections = ['Detailed Explanation', 'Additional Context', 'Examples', 'Historical Context'];
        return collapsibleSections.some(section => heading.textContent.includes(section));
    }

    makeProfessionalCollapsible(heading, index) {
        const content = [];
        let nextElement = heading.nextElementSibling;

        // Collect content until next heading of same or higher level
        while (nextElement && !this.isHeadingOfSameOrHigherLevel(nextElement, heading)) {
            content.push(nextElement);
            nextElement = nextElement.nextElementSibling;
        }

        if (content.length > 0) {
            const section = document.createElement('div');
            section.className = 'professional-collapsible-section collapsible-section';

            const header = document.createElement('div');
            header.className = 'professional-collapsible-header collapsible-header';
            header.innerHTML = `
                <div class="header-content">
                    <i class="section-icon fas fa-book-open"></i>
                    ${heading.innerHTML}
                </div>
                <div class="header-controls">
                    <span class="expand-hint">Click to expand</span>
                    <i class="fas fa-chevron-down collapsible-icon"></i>
                </div>
            `;

            const contentDiv = document.createElement('div');
            contentDiv.className = 'professional-collapsible-content collapsible-content';

            content.forEach(element => {
                contentDiv.appendChild(element);
            });

            section.appendChild(header);
            section.appendChild(contentDiv);

            heading.parentNode.replaceChild(section, heading);

            // Add enhanced click handler
            header.addEventListener('click', () => {
                section.classList.toggle('expanded');
                contentDiv.classList.toggle('active');

                const hint = header.querySelector('.expand-hint');
                hint.textContent = section.classList.contains('expanded') ? 'Click to collapse' : 'Click to expand';
            });
        }
    }

    addInteractiveCitations(container) {
        const citations = container.querySelectorAll('.enhanced-citation');
        citations.forEach(citation => {
            citation.addEventListener('click', (e) => {
                e.preventDefault();
                const page = citation.dataset.page;
                this.showCitationDetails(page, citation);
            });

            citation.addEventListener('mouseenter', () => {
                citation.style.transform = 'scale(1.05)';
                citation.style.boxShadow = '0 2px 8px rgba(37, 99, 235, 0.3)';
            });

            citation.addEventListener('mouseleave', () => {
                citation.style.transform = 'scale(1)';
                citation.style.boxShadow = 'none';
            });
        });
    }

    showCitationDetails(page, element) {
        // Create citation popup
        const popup = document.createElement('div');
        popup.className = 'citation-popup';
        popup.innerHTML = `
            <div class="citation-content">
                <h4><i class="fas fa-book"></i> Source Reference</h4>
                <p><strong>Page:</strong> ${page}</p>
                <p><strong>Source:</strong> CBSE Class 9 Textbook</p>
                <p><strong>Reliability:</strong> Official Curriculum Content</p>
                <button class="close-citation"><i class="fas fa-times"></i></button>
            </div>
        `;

        document.body.appendChild(popup);

        // Position popup
        const rect = element.getBoundingClientRect();
        popup.style.position = 'fixed';
        popup.style.top = `${rect.bottom + 10}px`;
        popup.style.left = `${rect.left}px`;
        popup.style.zIndex = '10000';

        // Add close handler
        popup.querySelector('.close-citation').addEventListener('click', () => {
            document.body.removeChild(popup);
        });

        // Auto-close after 5 seconds
        setTimeout(() => {
            if (document.body.contains(popup)) {
                document.body.removeChild(popup);
            }
        }, 5000);
    }

    addProfessionalCopyButtons(container) {
        const codeBlocks = container.querySelectorAll('pre code, .diagram');
        codeBlocks.forEach(block => {
            const button = document.createElement('button');
            button.className = 'professional-copy-btn copy-code-btn';
            button.innerHTML = '<i class="fas fa-copy"></i><span class="copy-text">Copy</span>';
            button.title = 'Copy to clipboard';

            button.addEventListener('click', async () => {
                const text = block.textContent;
                try {
                    await navigator.clipboard.writeText(text);
                    button.innerHTML = '<i class="fas fa-check"></i><span class="copy-text">Copied!</span>';
                    button.classList.add('copied');

                    setTimeout(() => {
                        button.innerHTML = '<i class="fas fa-copy"></i><span class="copy-text">Copy</span>';
                        button.classList.remove('copied');
                    }, 2000);
                } catch (err) {
                    console.error('Failed to copy text:', err);
                }
            });

            const parent = block.parentElement;
            parent.style.position = 'relative';
            parent.appendChild(button);
        });
    }

    addProfessionalTableInteractions(container) {
        const tables = container.querySelectorAll('table');
        tables.forEach(table => {
            // Add professional table wrapper
            const wrapper = document.createElement('div');
            wrapper.className = 'professional-table-wrapper';
            table.parentNode.insertBefore(wrapper, table);
            wrapper.appendChild(table);

            // Add table controls
            const controls = document.createElement('div');
            controls.className = 'table-controls';
            controls.innerHTML = `
                <button class="table-btn" title="Export Table"><i class="fas fa-download"></i></button>
                <button class="table-btn" title="Full Screen"><i class="fas fa-expand"></i></button>
            `;
            wrapper.insertBefore(controls, table);

            // Enhanced sorting with visual indicators
            this.enhanceTable(table);
        });
    }

    addVisualDiagramInteractions(container) {
        const diagrams = container.querySelectorAll('.diagram, pre[class*="diagram"]');
        diagrams.forEach(diagram => {
            diagram.addEventListener('click', () => {
                this.expandDiagram(diagram);
            });

            diagram.style.cursor = 'pointer';
            diagram.title = 'Click to expand diagram';
        });
    }

    expandDiagram(diagram) {
        const modal = document.createElement('div');
        modal.className = 'diagram-modal';
        modal.innerHTML = `
            <div class="diagram-modal-content">
                <div class="diagram-modal-header">
                    <h3><i class="fas fa-chart-line"></i> Expanded Diagram</h3>
                    <button class="close-modal"><i class="fas fa-times"></i></button>
                </div>
                <div class="diagram-modal-body">
                    ${diagram.outerHTML}
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        modal.querySelector('.close-modal').addEventListener('click', () => {
            document.body.removeChild(modal);
        });

        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                document.body.removeChild(modal);
            }
        });
    }

    validateFormattingQuality(container) {
        const qualityChecks = {
            hasProperHeadings: container.querySelectorAll('h1, h2, h3, h4').length > 0,
            hasCitations: container.querySelectorAll('.enhanced-citation, .academic-citation').length > 0,
            hasStructuredContent: container.querySelectorAll('ul, ol, table, blockquote').length > 0,
            hasSubjectTerms: container.querySelectorAll('.professional-term, .subject-term').length > 0
        };

        const qualityScore = Object.values(qualityChecks).filter(Boolean).length / Object.keys(qualityChecks).length;

        // Add quality indicator
        const qualityIndicator = document.createElement('div');
        qualityIndicator.className = `quality-indicator ${this.getQualityClass(qualityScore)}`;
        qualityIndicator.innerHTML = `
            <i class="fas fa-award"></i>
            <span>Formatting Quality: ${(qualityScore * 100).toFixed(0)}%</span>
        `;

        container.appendChild(qualityIndicator);

        return qualityScore;
    }

    getQualityClass(score) {
        if (score >= 0.8) return 'excellent-quality';
        if (score >= 0.6) return 'good-quality';
        if (score >= 0.4) return 'fair-quality';
        return 'needs-improvement';
    }

    createFallbackMessage(content) {
        const container = document.createElement('div');
        container.className = 'enhanced-message fallback';
        
        const messageContent = document.createElement('div');
        messageContent.className = 'enhanced-message-content';
        messageContent.innerHTML = `<p>${this.escapeHtml(content)}</p>`;
        
        container.appendChild(messageContent);
        return container;
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // Public API methods
    formatMessage(content, options = {}) {
        return this.formatResponse(content, options);
    }

    updateTheme(theme) {
        document.documentElement.setAttribute('data-theme', theme);
    }

    getFormattingStats(content) {
        return {
            wordCount: content.split(/\s+/).length,
            characterCount: content.length,
            subject: this.detectSubject(content),
            hasMarkdown: /[*_`#\[\]]/g.test(content),
            hasMath: /\$|\\\(|\\\[/g.test(content),
            hasCode: /```|`/g.test(content)
        };
    }
}

// Initialize global formatter instance when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    console.log('🎨 Initializing Enhanced Formatter...');
    window.enhancedFormatter = new EnhancedFormatter();
    console.log('✅ Enhanced Formatter initialized successfully');
});

// Also initialize immediately if DOM is already loaded
if (document.readyState === 'loading') {
    // DOM is still loading, wait for DOMContentLoaded
} else {
    // DOM is already loaded
    console.log('🎨 Initializing Enhanced Formatter (DOM already loaded)...');
    window.enhancedFormatter = new EnhancedFormatter();
    console.log('✅ Enhanced Formatter initialized successfully');
}

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = EnhancedFormatter;
}
