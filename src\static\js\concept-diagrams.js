class ConceptDiagramGenerator {
    constructor() {
        this.svgNamespace = "http://www.w3.org/2000/svg";
        this.diagramTypes = {
            flowchart: this.generateFlowchart.bind(this),
            organization: this.generateOrgChart.bind(this),
            timeline: this.generateTimeline.bind(this),
            process: this.generateProcessDiagram.bind(this),
            comparison: this.generateComparisonDiagram.bind(this),
            causeEffect: this.generateCauseEffectDiagram.bind(this),
            conceptMap: this.generateConceptMap.bind(this),
            timeline3D: this.generateTimeline3D.bind(this)
        };
    }

    generateDiagram(type, data, container) {
        if (this.diagramTypes[type]) {
            return this.diagramTypes[type](data, container);
        }
        console.error(`Unsupported diagram type: ${type}`);
        return null;
    }

    generateFlowchart(data, container) {
        const svg = this.createSVG();
        const { nodes, connections } = data;

        // Create nodes
        nodes.forEach((node, index) => {
            const nodeElement = this.createNode(node, index);
            svg.appendChild(nodeElement);
        });

        // Create connections
        connections.forEach(connection => {
            const path = this.createConnection(
                nodes[connection.from],
                nodes[connection.to],
                connection.type
            );
            svg.appendChild(path);
        });

        container.appendChild(svg);
        this.makeInteractive(svg);
    }

    generateOrgChart(data, container) {
        const svg = this.createSVG();
        const rootNode = data.root;
        
        this.layoutOrgChart(rootNode, {x: 400, y: 50}, svg);
        container.appendChild(svg);
        this.makeInteractive(svg);
    }

    generateTimeline(data, container) {
        const svg = this.createSVG();
        const { events, startYear, endYear } = data;

        const timelineBase = this.createTimelineBase(startYear, endYear);
        svg.appendChild(timelineBase);

        events.forEach(event => {
            const eventMarker = this.createTimelineEvent(event);
            svg.appendChild(eventMarker);
        });

        container.appendChild(svg);
        this.makeInteractive(svg);
    }

    generateProcessDiagram(data, container) {
        const svg = this.createSVG();
        const { steps, cycles } = data;

        if (cycles) {
            this.createCyclicProcess(steps, svg);
        } else {
            this.createLinearProcess(steps, svg);
        }

        container.appendChild(svg);
        this.makeInteractive(svg);
    }

    generateComparisonDiagram(data, container) {
        const svg = this.createSVG();
        const { items, categories } = data;
        
        // Create comparison matrix
        const matrix = document.createElementNS(this.svgNamespace, "g");
        const cellWidth = 120;
        const cellHeight = 80;
        
        // Add headers
        categories.forEach((category, i) => {
            const header = this.createTextElement(category, {
                x: (i + 1) * cellWidth + cellWidth/2,
                y: 40,
                class: 'comparison-header'
            });
            matrix.appendChild(header);
        });
        
        // Add items and comparisons
        items.forEach((item, rowIndex) => {
            const itemLabel = this.createTextElement(item.name, {
                x: cellWidth/2,
                y: (rowIndex + 1) * cellHeight + 40,
                class: 'comparison-item'
            });
            matrix.appendChild(itemLabel);
            
            // Add comparison cells
            item.comparisons.forEach((comparison, colIndex) => {
                const cell = this.createComparisonCell(comparison, {
                    x: (colIndex + 1) * cellWidth,
                    y: (rowIndex + 1) * cellHeight,
                    width: cellWidth,
                    height: cellHeight
                });
                matrix.appendChild(cell);
            });
        });
        
        svg.appendChild(matrix);
        container.appendChild(svg);
        this.makeInteractive(svg);
    }

    generateCauseEffectDiagram(data, container) {
        const svg = this.createSVG();
        const { cause, effects } = data;
        
        // Create central cause node
        const causeNode = this.createNode({
            label: cause,
            x: 400,
            y: 300,
            class: 'cause-node'
        });
        svg.appendChild(causeNode);
        
        // Create effect nodes in a circular pattern
        effects.forEach((effect, index) => {
            const angle = (2 * Math.PI * index) / effects.length;
            const radius = 200;
            const x = 400 + radius * Math.cos(angle);
            const y = 300 + radius * Math.sin(angle);
            
            const effectNode = this.createNode({
                label: effect,
                x: x,
                y: y,
                class: 'effect-node'
            });
            
            // Create arrow from cause to effect
            const arrow = this.createArrow(400, 300, x, y);
            
            svg.appendChild(arrow);
            svg.appendChild(effectNode);
        });
        
        container.appendChild(svg);
        this.makeInteractive(svg);
    }

    generateConceptMap(data, container) {
        const svg = this.createSVG();
        const { concepts, relationships } = data;
        
        // Create concept nodes with force-directed layout
        const simulation = d3.forceSimulation(concepts)
            .force("charge", d3.forceManyBody().strength(-300))
            .force("center", d3.forceCenter(400, 300))
            .force("collision", d3.forceCollide().radius(60));
        
        // Add concept nodes
        concepts.forEach(concept => {
            const node = this.createNode({
                label: concept.name,
                x: concept.x,
                y: concept.y,
                class: `concept-node ${concept.type}`
            });
            svg.appendChild(node);
        });
        
        // Add relationship arrows
        relationships.forEach(rel => {
            const source = concepts.find(c => c.id === rel.source);
            const target = concepts.find(c => c.id === rel.target);
            
            const arrow = this.createArrow(
                source.x, source.y,
                target.x, target.y,
                rel.label
            );
            svg.appendChild(arrow);
        });
        
        container.appendChild(svg);
        this.makeInteractive(svg);
    }

    generateTimeline3D(data, container) {
        const svg = this.createSVG();
        const { events } = data;
        
        // Create 3D perspective effect
        const perspective = document.createElementNS(this.svgNamespace, "g");
        perspective.setAttribute("transform", "matrix(1,0.2,-0.5,1,200,0)");
        
        // Add timeline base
        const base = this.createPath("M 0 300 L 600 300", "timeline-base");
        perspective.appendChild(base);
        
        // Add events with 3D effect
        events.forEach((event, index) => {
            const x = index * 100 + 50;
            const y = 300;
            
            // Create 3D event marker
            const marker = this.create3DMarker(x, y, event);
            perspective.appendChild(marker);
            
            // Add event details
            const details = this.createEventDetails(x, y - 50, event);
            perspective.appendChild(details);
        });
        
        svg.appendChild(perspective);
        container.appendChild(svg);
        this.makeInteractive(svg);
    }

    // Helper methods
    createSVG() {
        const svg = document.createElementNS(this.svgNamespace, "svg");
        svg.setAttribute("width", "800");
        svg.setAttribute("height", "600");
        svg.setAttribute("viewBox", "0 0 800 600");
        return svg;
    }

    createNode(node, index) {
        const group = document.createElementNS(this.svgNamespace, "g");
        const rect = document.createElementNS(this.svgNamespace, "rect");
        const text = document.createElementNS(this.svgNamespace, "text");

        // Position calculation
        const x = node.x || (index * 150 + 50);
        const y = node.y || 100;

        // Rectangle styling
        rect.setAttribute("x", x);
        rect.setAttribute("y", y);
        rect.setAttribute("width", "120");
        rect.setAttribute("height", "60");
        rect.setAttribute("rx", "10");
        rect.setAttribute("ry", "10");
        rect.setAttribute("fill", "white");
        rect.setAttribute("stroke", "#3b82f6");
        rect.setAttribute("stroke-width", "2");

        // Text styling
        text.setAttribute("x", x + 60);
        text.setAttribute("y", y + 35);
        text.setAttribute("text-anchor", "middle");
        text.setAttribute("fill", "#1e293b");
        text.textContent = node.label;

        group.appendChild(rect);
        group.appendChild(text);
        return group;
    }

    createConnection(fromNode, toNode, type = "arrow") {
        const path = document.createElementNS(this.svgNamespace, "path");
        
        // Calculate path
        const startX = fromNode.x + 120;
        const startY = fromNode.y + 30;
        const endX = toNode.x;
        const endY = toNode.y + 30;
        const controlX = (startX + endX) / 2;

        const d = `M ${startX} ${startY} 
                   C ${controlX} ${startY}, ${controlX} ${endY}, ${endX} ${endY}`;

        path.setAttribute("d", d);
        path.setAttribute("fill", "none");
        path.setAttribute("stroke", "#64748b");
        path.setAttribute("stroke-width", "2");
        path.setAttribute("marker-end", "url(#arrow)");

        return path;
    }

    layoutOrgChart(node, position, svg, level = 0) {
        const nodeElement = this.createNode({
            label: node.title,
            x: position.x,
            y: position.y
        });
        svg.appendChild(nodeElement);

        if (node.children) {
            const childWidth = 150;
            const startX = position.x - (node.children.length * childWidth) / 2;

            node.children.forEach((child, index) => {
                const childPos = {
                    x: startX + index * childWidth,
                    y: position.y + 100
                };

                this.layoutOrgChart(child, childPos, svg, level + 1);
                
                // Create connection line
                const connection = this.createConnection(
                    {x: position.x, y: position.y},
                    {x: childPos.x, y: childPos.y}
                );
                svg.appendChild(connection);
            });
        }
    }

    createTimelineBase(startYear, endYear) {
        const group = document.createElementNS(this.svgNamespace, "g");
        const line = document.createElementNS(this.svgNamespace, "line");

        line.setAttribute("x1", "50");
        line.setAttribute("y1", "300");
        line.setAttribute("x2", "750");
        line.setAttribute("y2", "300");
        line.setAttribute("stroke", "#94a3b8");
        line.setAttribute("stroke-width", "2");

        // Add year markers
        const years = endYear - startYear;
        const spacing = 700 / years;

        for (let i = 0; i <= years; i++) {
            const x = 50 + i * spacing;
            const yearMark = document.createElementNS(this.svgNamespace, "g");
            
            const tick = document.createElementNS(this.svgNamespace, "line");
            tick.setAttribute("x1", x);
            tick.setAttribute("y1", "295");
            tick.setAttribute("x2", x);
            tick.setAttribute("y2", "305");
            tick.setAttribute("stroke", "#94a3b8");
            tick.setAttribute("stroke-width", "2");

            const yearText = document.createElementNS(this.svgNamespace, "text");
            yearText.setAttribute("x", x);
            yearText.setAttribute("y", "325");
            yearText.setAttribute("text-anchor", "middle");
            yearText.setAttribute("fill", "#64748b");
            yearText.textContent = startYear + i;

            yearMark.appendChild(tick);
            yearMark.appendChild(yearText);
            group.appendChild(yearMark);
        }

        group.appendChild(line);
        return group;
    }

    createTimelineEvent(event) {
        const group = document.createElementNS(this.svgNamespace, "g");
        const circle = document.createElementNS(this.svgNamespace, "circle");
        const text = document.createElementNS(this.svgNamespace, "text");

        circle.setAttribute("cx", event.x);
        circle.setAttribute("cy", "300");
        circle.setAttribute("r", "8");
        circle.setAttribute("fill", "#3b82f6");

        text.setAttribute("x", event.x);
        text.setAttribute("y", event.position === "top" ? "270" : "340");
        text.setAttribute("text-anchor", "middle");
        text.setAttribute("fill", "#1e293b");
        text.textContent = event.label;

        group.appendChild(circle);
        group.appendChild(text);
        return group;
    }

    createCyclicProcess(steps, svg) {
        const centerX = 400;
        const centerY = 300;
        const radius = 200;

        steps.forEach((step, index) => {
            const angle = (2 * Math.PI * index) / steps.length;
            const x = centerX + radius * Math.cos(angle);
            const y = centerY + radius * Math.sin(angle);

            const node = this.createNode({
                label: step.label,
                x: x - 60,
                y: y - 30
            });
            svg.appendChild(node);

            // Create curved connection to next step
            const nextIndex = (index + 1) % steps.length;
            const nextAngle = (2 * Math.PI * nextIndex) / steps.length;
            const nextX = centerX + radius * Math.cos(nextAngle);
            const nextY = centerY + radius * Math.sin(nextAngle);

            const connection = this.createConnection(
                {x: x - 60, y: y - 30},
                {x: nextX - 60, y: nextY - 30},
                "curve"
            );
            svg.appendChild(connection);
        });
    }

    createLinearProcess(steps, svg) {
        steps.forEach((step, index) => {
            const node = this.createNode({
                label: step.label,
                x: 50 + index * 180,
                y: 100
            });
            svg.appendChild(node);

            if (index < steps.length - 1) {
                const connection = this.createConnection(
                    {x: 50 + index * 180, y: 100},
                    {x: 50 + (index + 1) * 180, y: 100}
                );
                svg.appendChild(connection);
            }
        });
    }

    createComparisonCell(data, props) {
        const group = document.createElementNS(this.svgNamespace, "g");
        const rect = document.createElementNS(this.svgNamespace, "rect");
        const text = document.createElementNS(this.svgNamespace, "text");
        
        Object.assign(rect, {
            x: props.x,
            y: props.y,
            width: props.width,
            height: props.height,
            class: `comparison-cell ${data.type}`
        });
        
        Object.assign(text, {
            x: props.x + props.width/2,
            y: props.y + props.height/2,
            textContent: data.value,
            class: 'comparison-value'
        });
        
        group.appendChild(rect);
        group.appendChild(text);
        return group;
    }

    createArrow(x1, y1, x2, y2, label = '') {
        const group = document.createElementNS(this.svgNamespace, "g");
        const line = document.createElementNS(this.svgNamespace, "line");
        const marker = document.createElementNS(this.svgNamespace, "path");
        
        // Create line
        Object.assign(line, {
            x1, y1, x2, y2,
            class: 'arrow-line'
        });
        
        // Create arrowhead
        const angle = Math.atan2(y2 - y1, x2 - x1);
        const arrowSize = 10;
        const arrowPath = `M ${x2} ${y2} L ${x2 - arrowSize * Math.cos(angle - Math.PI/6)} ${y2 - arrowSize * Math.sin(angle - Math.PI/6)} L ${x2 - arrowSize * Math.cos(angle + Math.PI/6)} ${y2 - arrowSize * Math.sin(angle + Math.PI/6)} Z`;
        marker.setAttribute("d", arrowPath);
        marker.setAttribute("class", "arrow-head");
        
        // Add label if provided
        if (label) {
            const text = this.createTextElement(label, {
                x: (x1 + x2)/2,
                y: (y1 + y2)/2 - 10,
                class: 'relationship-label'
            });
            group.appendChild(text);
        }
        
        group.appendChild(line);
        group.appendChild(marker);
        return group;
    }

    create3DMarker(x, y, event) {
        const group = document.createElementNS(this.svgNamespace, "g");
        
        // Create 3D cylinder effect
        const cylinder = document.createElementNS(this.svgNamespace, "path");
        const cylinderPath = `
            M ${x-10} ${y}
            L ${x-10} ${y-30}
            A 10 5 0 0 1 ${x+10} ${y-30}
            L ${x+10} ${y}
            A 10 5 0 0 1 ${x-10} ${y}
            M ${x-10} ${y-30}
            A 10 5 0 0 1 ${x+10} ${y-30}
        `;
        cylinder.setAttribute("d", cylinderPath);
        cylinder.setAttribute("class", `event-marker ${event.type}`);
        
        group.appendChild(cylinder);
        return group;
    }

    createEventDetails(x, y, event) {
        const group = document.createElementNS(this.svgNamespace, "g");
        
        // Add date
        const date = this.createTextElement(event.date, {
            x: x,
            y: y,
            class: 'event-date'
        });
        
        // Add title
        const title = this.createTextElement(event.title, {
            x: x,
            y: y + 20,
            class: 'event-title'
        });
        
        group.appendChild(date);
        group.appendChild(title);
        return group;
    }

    makeInteractive(svg) {
        // Add zoom and pan functionality
        let isPanning = false;
        let startPoint = {x: 0, y: 0};
        let viewBox = {x: 0, y: 0, w: 800, h: 600};

        svg.addEventListener('mousedown', (e) => {
            isPanning = true;
            startPoint = {
                x: e.clientX - viewBox.x,
                y: e.clientY - viewBox.y
            };
        });

        svg.addEventListener('mousemove', (e) => {
            if (isPanning) {
                viewBox.x = e.clientX - startPoint.x;
                viewBox.y = e.clientY - startPoint.y;
                svg.setAttribute('viewBox', `${viewBox.x} ${viewBox.y} ${viewBox.w} ${viewBox.h}`);
            }
        });

        svg.addEventListener('mouseup', () => {
            isPanning = false;
        });

        // Add zoom on wheel
        svg.addEventListener('wheel', (e) => {
            e.preventDefault();
            const scale = e.deltaY > 0 ? 1.1 : 0.9;
            viewBox.w *= scale;
            viewBox.h *= scale;
            svg.setAttribute('viewBox', `${viewBox.x} ${viewBox.y} ${viewBox.w} ${viewBox.h}`);
        });

        // Add hover effects
        const nodes = svg.querySelectorAll('g');
        nodes.forEach(node => {
            node.addEventListener('mouseenter', () => {
                const rect = node.querySelector('rect');
                if (rect) {
                    rect.setAttribute('fill', '#f1f5f9');
                    rect.setAttribute('stroke-width', '3');
                }
            });

            node.addEventListener('mouseleave', () => {
                const rect = node.querySelector('rect');
                if (rect) {
                    rect.setAttribute('fill', 'white');
                    rect.setAttribute('stroke-width', '2');
                }
            });
        });
    }
}

// Initialize the diagram generator
document.addEventListener('DOMContentLoaded', () => {
    window.conceptDiagramGenerator = new ConceptDiagramGenerator();
}); 