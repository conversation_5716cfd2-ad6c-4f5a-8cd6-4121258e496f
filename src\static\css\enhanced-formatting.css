/* Enhanced Educational RAG Formatting System */
/* ChatGPT-like Rich Text Formatting with Educational Focus */

/* CSS Variables for Enhanced Typography */
:root {
    /* Educational Color Palette */
    --edu-primary: #2563eb;
    --edu-secondary: #7c3aed;
    --edu-success: #059669;
    --edu-warning: #d97706;
    --edu-error: #dc2626;
    --edu-info: #0891b2;
    
    /* Subject-Specific Colors */
    --subject-history: #8b5cf6;
    --subject-geography: #10b981;
    --subject-economics: #f59e0b;
    --subject-politics: #ef4444;
    
    /* Typography Scale */
    --text-xs: 0.75rem;
    --text-sm: 0.875rem;
    --text-base: 1rem;
    --text-lg: 1.125rem;
    --text-xl: 1.25rem;
    --text-2xl: 1.5rem;
    --text-3xl: 1.875rem;
    --text-4xl: 2.25rem;
    
    /* Spacing Scale */
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-12: 3rem;
    
    /* Enhanced Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

/* Enhanced Message Formatting */
.enhanced-message {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    line-height: 1.7;
    color: var(--chat-text-primary-light);
    max-width: none;
}

/* Enhanced Fallback Formatting Classes */
.enhanced-h1 {
    font-size: var(--text-3xl);
    font-weight: 700;
    color: var(--edu-primary);
    margin: var(--space-6) 0 var(--space-4) 0;
    border-bottom: 3px solid var(--edu-primary);
    padding-bottom: var(--space-2);
}

.enhanced-h2 {
    font-size: var(--text-2xl);
    font-weight: 700;
    color: var(--edu-primary);
    margin: var(--space-6) 0 var(--space-4) 0;
    position: relative;
    padding-left: var(--space-4);
}

.enhanced-h2::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 100%;
    background: var(--edu-primary);
    border-radius: 2px;
}

.enhanced-h3 {
    font-size: var(--text-xl);
    font-weight: 600;
    color: var(--edu-secondary);
    margin: var(--space-4) 0 var(--space-3) 0;
}

.enhanced-paragraph {
    margin-bottom: var(--space-4);
    text-align: justify;
    hyphens: auto;
    line-height: 1.7;
}

.enhanced-blockquote {
    border-left: 4px solid var(--edu-info);
    background: linear-gradient(135deg, rgba(8, 145, 178, 0.05) 0%, rgba(8, 145, 178, 0.02) 100%);
    padding: var(--space-4);
    margin: var(--space-4) 0;
    border-radius: 0 8px 8px 0;
    font-style: italic;
    position: relative;
}

.enhanced-code {
    background: #1e293b;
    color: #e2e8f0;
    padding: var(--space-4);
    border-radius: 8px;
    overflow-x: auto;
    margin: var(--space-4) 0;
    border-left: 4px solid var(--edu-primary);
}

.enhanced-inline-code {
    background: #f1f5f9;
    color: #475569;
    padding: 0.2em 0.4em;
    border-radius: 4px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.9em;
    border: 1px solid #e2e8f0;
}

.enhanced-ul {
    margin: var(--space-4) 0;
    padding-left: var(--space-6);
}

.enhanced-ol {
    margin: var(--space-4) 0;
    padding-left: var(--space-6);
}

.enhanced-ul-item {
    position: relative;
    margin-bottom: var(--space-2);
    list-style: none;
}

.enhanced-ul-item::before {
    content: '•';
    color: var(--edu-primary);
    font-weight: bold;
    position: absolute;
    left: -var(--space-4);
    font-size: var(--text-lg);
}

.enhanced-ol-item {
    margin-bottom: var(--space-2);
    counter-increment: list-counter;
}

.enhanced-ol-item::marker {
    color: var(--edu-primary);
    font-weight: 700;
}

/* Basic Formatting Fallback Classes */
.basic-h1 {
    font-size: var(--text-2xl);
    font-weight: 700;
    color: var(--edu-primary);
    margin: var(--space-4) 0 var(--space-3) 0;
    border-bottom: 2px solid var(--edu-primary);
    padding-bottom: var(--space-1);
}

.basic-h2 {
    font-size: var(--text-xl);
    font-weight: 600;
    color: var(--edu-primary);
    margin: var(--space-4) 0 var(--space-3) 0;
}

.basic-h3 {
    font-size: var(--text-lg);
    font-weight: 600;
    color: var(--edu-secondary);
    margin: var(--space-3) 0 var(--space-2) 0;
}

.basic-paragraph {
    margin-bottom: var(--space-3);
    line-height: 1.6;
}

.basic-bold {
    font-weight: 700;
    color: var(--edu-primary);
}

.basic-italic {
    font-style: italic;
    color: var(--edu-secondary);
}

.basic-blockquote {
    border-left: 3px solid var(--edu-info);
    background: rgba(8, 145, 178, 0.05);
    padding: var(--space-3);
    margin: var(--space-3) 0;
    border-radius: 0 6px 6px 0;
    font-style: italic;
}

.basic-code {
    background: #f1f5f9;
    color: #475569;
    padding: 0.2em 0.3em;
    border-radius: 3px;
    font-family: monospace;
    font-size: 0.9em;
}

.basic-ul-item,
.basic-ol-item {
    margin-bottom: var(--space-1);
    line-height: 1.5;
}

/* Enhanced Fallback Styling */
.enhanced-fallback {
    border-left: 4px solid var(--edu-warning);
    background: linear-gradient(135deg, rgba(217, 119, 6, 0.05) 0%, rgba(217, 119, 6, 0.02) 100%);
}

.basic-formatting {
    border-left: 4px solid var(--edu-info);
    background: linear-gradient(135deg, rgba(8, 145, 178, 0.05) 0%, rgba(8, 145, 178, 0.02) 100%);
}

.enhanced-message-content {
    padding: var(--space-6);
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 16px;
    border: 1px solid #e2e8f0;
    box-shadow: var(--shadow-md);
    position: relative;
    overflow: hidden;
}

.enhanced-message-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--edu-primary), var(--edu-secondary));
    border-radius: 16px 16px 0 0;
}

/* Typography Hierarchy */
.enhanced-message h1,
.enhanced-message h2,
.enhanced-message h3,
.enhanced-message h4,
.enhanced-message h5,
.enhanced-message h6 {
    font-weight: 700;
    margin-top: var(--space-6);
    margin-bottom: var(--space-4);
    line-height: 1.3;
    color: var(--chat-text-primary-light);
}

.enhanced-message h1 {
    font-size: var(--text-3xl);
    border-bottom: 3px solid var(--edu-primary);
    padding-bottom: var(--space-2);
}

.enhanced-message h2 {
    font-size: var(--text-2xl);
    color: var(--edu-primary);
    position: relative;
    padding-left: var(--space-4);
}

.enhanced-message h2::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 100%;
    background: var(--edu-primary);
    border-radius: 2px;
}

.enhanced-message h3 {
    font-size: var(--text-xl);
    color: var(--edu-secondary);
    font-weight: 600;
}

.enhanced-message h4 {
    font-size: var(--text-lg);
    color: var(--chat-text-primary-light);
    font-weight: 600;
}

/* Enhanced Paragraph Styling */
.enhanced-message p {
    margin-bottom: var(--space-4);
    text-align: justify;
    hyphens: auto;
}

.enhanced-message p:last-child {
    margin-bottom: 0;
}

/* Strong and Emphasis */
.enhanced-message strong,
.enhanced-message b {
    font-weight: 700;
    color: var(--edu-primary);
    background: linear-gradient(120deg, rgba(37, 99, 235, 0.1) 0%, rgba(37, 99, 235, 0.05) 100%);
    padding: 0.1em 0.3em;
    border-radius: 4px;
}

.enhanced-message em,
.enhanced-message i {
    font-style: italic;
    color: var(--edu-secondary);
    background: linear-gradient(120deg, rgba(124, 58, 237, 0.1) 0%, rgba(124, 58, 237, 0.05) 100%);
    padding: 0.1em 0.2em;
    border-radius: 3px;
}

/* Enhanced Lists */
.enhanced-message ul,
.enhanced-message ol {
    margin: var(--space-4) 0;
    padding-left: var(--space-6);
}

.enhanced-message ul li {
    position: relative;
    margin-bottom: var(--space-2);
    list-style: none;
}

.enhanced-message ul li::before {
    content: '•';
    color: var(--edu-primary);
    font-weight: bold;
    position: absolute;
    left: -var(--space-4);
    font-size: var(--text-lg);
}

.enhanced-message ol li {
    margin-bottom: var(--space-2);
    counter-increment: list-counter;
}

.enhanced-message ol {
    counter-reset: list-counter;
}

.enhanced-message ol li::marker {
    color: var(--edu-primary);
    font-weight: 700;
}

/* Code Blocks and Inline Code */
.enhanced-message code {
    background: #f1f5f9;
    color: #475569;
    padding: 0.2em 0.4em;
    border-radius: 4px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.9em;
    border: 1px solid #e2e8f0;
}

.enhanced-message pre {
    background: #1e293b;
    color: #e2e8f0;
    padding: var(--space-4);
    border-radius: 8px;
    overflow-x: auto;
    margin: var(--space-4) 0;
    border-left: 4px solid var(--edu-primary);
}

.enhanced-message pre code {
    background: none;
    color: inherit;
    padding: 0;
    border: none;
    font-size: var(--text-sm);
}

/* Blockquotes */
.enhanced-message blockquote {
    border-left: 4px solid var(--edu-info);
    background: linear-gradient(135deg, rgba(8, 145, 178, 0.05) 0%, rgba(8, 145, 178, 0.02) 100%);
    padding: var(--space-4);
    margin: var(--space-4) 0;
    border-radius: 0 8px 8px 0;
    font-style: italic;
    position: relative;
}

.enhanced-message blockquote::before {
    content: '"';
    font-size: var(--text-4xl);
    color: var(--edu-info);
    position: absolute;
    top: -var(--space-2);
    left: var(--space-2);
    opacity: 0.3;
}

/* Tables */
.enhanced-message table {
    width: 100%;
    border-collapse: collapse;
    margin: var(--space-4) 0;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.enhanced-message th,
.enhanced-message td {
    padding: var(--space-3) var(--space-4);
    text-align: left;
    border-bottom: 1px solid #e2e8f0;
}

.enhanced-message th {
    background: var(--edu-primary);
    color: white;
    font-weight: 600;
    text-transform: uppercase;
    font-size: var(--text-sm);
    letter-spacing: 0.05em;
}

.enhanced-message tr:hover {
    background: #f8fafc;
}

/* Mathematical Expressions */
.enhanced-message .katex {
    font-size: 1.1em;
}

.enhanced-message .katex-display {
    margin: var(--space-4) 0;
    text-align: center;
    background: #f8fafc;
    padding: var(--space-4);
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

/* Subject-Specific Styling */
.subject-history .enhanced-message-content::before {
    background: linear-gradient(90deg, var(--subject-history), #a855f7);
}

.subject-geography .enhanced-message-content::before {
    background: linear-gradient(90deg, var(--subject-geography), #34d399);
}

.subject-economics .enhanced-message-content::before {
    background: linear-gradient(90deg, var(--subject-economics), #fbbf24);
}

.subject-politics .enhanced-message-content::before {
    background: linear-gradient(90deg, var(--subject-politics), #f87171);
}

/* Interactive Elements */
.collapsible-section {
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    margin: var(--space-4) 0;
    overflow: hidden;
    transition: all 0.3s ease;
}

.collapsible-header {
    background: #f8fafc;
    padding: var(--space-3) var(--space-4);
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 600;
    color: var(--edu-primary);
    transition: all 0.2s ease;
}

.collapsible-header:hover {
    background: #f1f5f9;
}

.collapsible-content {
    padding: var(--space-4);
    display: none;
    animation: slideDown 0.3s ease;
}

.collapsible-content.active {
    display: block;
}

.collapsible-icon {
    transition: transform 0.3s ease;
}

.collapsible-section.expanded .collapsible-icon {
    transform: rotate(180deg);
}

/* Animations */
@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.enhanced-message {
    animation: fadeInUp 0.5s ease-out;
}

/* Dark Mode Support */
[data-theme="dark"] .enhanced-message {
    color: var(--chat-text-primary-dark);
}

[data-theme="dark"] .enhanced-message-content {
    background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
    border-color: #475569;
}

[data-theme="dark"] .enhanced-message h1,
[data-theme="dark"] .enhanced-message h2,
[data-theme="dark"] .enhanced-message h3,
[data-theme="dark"] .enhanced-message h4 {
    color: #e2e8f0;
}

[data-theme="dark"] .enhanced-message code {
    background: #374151;
    color: #d1d5db;
    border-color: #4b5563;
}

[data-theme="dark"] .enhanced-message table {
    background: #374151;
}

[data-theme="dark"] .enhanced-message th {
    background: var(--edu-primary);
}

[data-theme="dark"] .enhanced-message tr:hover {
    background: #4b5563;
}

/* Diagram Styling */
.diagram-container {
    margin: var(--space-6) 0;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    overflow: hidden;
    background: white;
    box-shadow: var(--shadow-md);
}

.diagram-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-3) var(--space-4);
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-bottom: 1px solid #e2e8f0;
}

.diagram-header h4 {
    margin: 0;
    color: var(--edu-primary);
    font-size: var(--text-base);
    font-weight: 600;
}

.diagram-controls {
    display: flex;
    gap: var(--space-2);
}

.diagram-btn {
    width: 32px;
    height: 32px;
    border: 1px solid #e2e8f0;
    background: white;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--chat-text-secondary-light);
    transition: all 0.2s ease;
}

.diagram-btn:hover {
    background: #f8fafc;
    color: var(--edu-primary);
    transform: translateY(-1px);
}

.mermaid {
    padding: var(--space-4);
    text-align: center;
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.diagram-error {
    color: var(--edu-error);
    font-style: italic;
    padding: var(--space-4);
}

/* Educational Pattern Styling */
.edu-date {
    background: linear-gradient(120deg, rgba(37, 99, 235, 0.1) 0%, rgba(37, 99, 235, 0.05) 100%);
    color: var(--edu-primary);
    padding: 0.1em 0.3em;
    border-radius: 4px;
    font-weight: 600;
}

.edu-measurement {
    background: linear-gradient(120deg, rgba(5, 150, 105, 0.1) 0%, rgba(5, 150, 105, 0.05) 100%);
    color: var(--edu-success);
    padding: 0.1em 0.3em;
    border-radius: 4px;
    font-weight: 500;
}

.edu-concept {
    background: linear-gradient(120deg, rgba(124, 58, 237, 0.1) 0%, rgba(124, 58, 237, 0.05) 100%);
    color: var(--edu-secondary);
    padding: 0.1em 0.3em;
    border-radius: 4px;
    font-weight: 500;
}

.edu-scientific {
    color: var(--edu-info);
    font-style: italic;
    font-weight: 500;
}

.subject-term {
    padding: 0.1em 0.3em;
    border-radius: 4px;
    font-weight: 600;
}

.subject-term.subject-history {
    background: linear-gradient(120deg, rgba(139, 92, 246, 0.1) 0%, rgba(139, 92, 246, 0.05) 100%);
    color: var(--subject-history);
}

.subject-term.subject-geography {
    background: linear-gradient(120deg, rgba(16, 185, 129, 0.1) 0%, rgba(16, 185, 129, 0.05) 100%);
    color: var(--subject-geography);
}

.subject-term.subject-economics {
    background: linear-gradient(120deg, rgba(245, 158, 11, 0.1) 0%, rgba(245, 158, 11, 0.05) 100%);
    color: var(--subject-economics);
}

.subject-term.subject-politics {
    background: linear-gradient(120deg, rgba(239, 68, 68, 0.1) 0%, rgba(239, 68, 68, 0.05) 100%);
    color: var(--subject-politics);
}

/* Message Metadata */
.message-metadata {
    display: flex;
    gap: var(--space-3);
    align-items: center;
    margin-top: var(--space-4);
    padding-top: var(--space-3);
    border-top: 1px solid #e2e8f0;
    font-size: var(--text-sm);
}

.subject-badge {
    padding: 0.25em 0.5em;
    border-radius: 12px;
    font-size: var(--text-xs);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.subject-badge.subject-history {
    background: var(--subject-history);
    color: white;
}

.subject-badge.subject-geography {
    background: var(--subject-geography);
    color: white;
}

.subject-badge.subject-economics {
    background: var(--subject-economics);
    color: white;
}

.subject-badge.subject-politics {
    background: var(--subject-politics);
    color: white;
}

.confidence-score {
    color: var(--edu-success);
    font-weight: 500;
}

.processing-time {
    color: var(--chat-text-secondary-light);
}

/* Copy Code Button */
.copy-code-btn {
    position: absolute;
    top: var(--space-2);
    right: var(--space-2);
    width: 32px;
    height: 32px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.2s ease;
}

pre:hover .copy-code-btn {
    opacity: 1;
}

.copy-code-btn:hover {
    background: rgba(0, 0, 0, 0.9);
}

/* Loading States */
.enhanced-message.loading {
    opacity: 0.7;
}

.enhanced-message.loading .enhanced-message-content::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* Enhanced Accessibility */
.enhanced-message:focus-within {
    outline: 2px solid var(--edu-primary);
    outline-offset: 2px;
}

.enhanced-message [tabindex="0"]:focus {
    outline: 2px solid var(--edu-primary);
    outline-offset: 2px;
    border-radius: 4px;
}

/* Professional Message Styling */
.professional-message {
    margin: var(--space-6) 0;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: var(--shadow-lg);
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border: 1px solid #e2e8f0;
}

.professional-message-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-4) var(--space-6);
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
    border-bottom: 1px solid #cbd5e1;
}

.subject-indicator {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    font-weight: 600;
    color: var(--edu-primary);
}

.subject-name {
    font-size: var(--text-lg);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.quality-badge {
    padding: 0.25em 0.75em;
    border-radius: 20px;
    font-size: var(--text-xs);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.quality-badge.professional {
    background: linear-gradient(135deg, var(--edu-success) 0%, #34d399 100%);
    color: white;
}

/* Professional Metadata */
.professional-metadata {
    display: flex;
    gap: var(--space-4);
    align-items: center;
    flex-wrap: wrap;
    margin-top: var(--space-6);
    padding-top: var(--space-4);
    border-top: 2px solid #e2e8f0;
}

.professional-subject-badge {
    padding: 0.5em 1em;
    border-radius: 25px;
    font-size: var(--text-sm);
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    box-shadow: var(--shadow-sm);
}

.professional-confidence {
    display: flex;
    align-items: center;
    gap: var(--space-1);
    padding: 0.25em 0.75em;
    border-radius: 15px;
    font-size: var(--text-sm);
    font-weight: 600;
}

.professional-confidence.high-confidence {
    background: linear-gradient(135deg, rgba(5, 150, 105, 0.1) 0%, rgba(5, 150, 105, 0.05) 100%);
    color: var(--edu-success);
}

.professional-confidence.medium-confidence {
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, rgba(245, 158, 11, 0.05) 100%);
    color: var(--edu-warning);
}

.professional-confidence.low-confidence {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.1) 0%, rgba(239, 68, 68, 0.05) 100%);
    color: var(--edu-error);
}

.professional-timing {
    display: flex;
    align-items: center;
    gap: var(--space-1);
    padding: 0.25em 0.75em;
    border-radius: 15px;
    font-size: var(--text-sm);
    color: var(--chat-text-secondary-light);
}

.professional-timing.fast-response {
    background: linear-gradient(135deg, rgba(5, 150, 105, 0.1) 0%, rgba(5, 150, 105, 0.05) 100%);
    color: var(--edu-success);
}

.academic-quality {
    display: flex;
    align-items: center;
    gap: var(--space-1);
    padding: 0.25em 0.75em;
    border-radius: 15px;
    font-size: var(--text-sm);
    background: linear-gradient(135deg, rgba(37, 99, 235, 0.1) 0%, rgba(37, 99, 235, 0.05) 100%);
    color: var(--edu-primary);
    font-weight: 600;
}

/* Professional Terms */
.professional-term {
    background: linear-gradient(120deg, rgba(37, 99, 235, 0.15) 0%, rgba(37, 99, 235, 0.08) 100%);
    color: var(--edu-primary);
    padding: 0.15em 0.4em;
    border-radius: 6px;
    font-weight: 600;
    border: 1px solid rgba(37, 99, 235, 0.2);
}

.professional-definition {
    background: linear-gradient(135deg, rgba(8, 145, 178, 0.08) 0%, rgba(8, 145, 178, 0.03) 100%);
    border-left: 4px solid var(--edu-info);
    padding: var(--space-4);
    margin: var(--space-4) 0;
    border-radius: 0 12px 12px 0;
    font-style: normal;
    position: relative;
    box-shadow: var(--shadow-sm);
}

.professional-definition::before {
    content: '"';
    font-size: var(--text-4xl);
    color: var(--edu-info);
    position: absolute;
    top: -var(--space-2);
    left: var(--space-3);
    opacity: 0.4;
    font-family: serif;
}

/* Enhanced Citations */
.enhanced-citation {
    background: linear-gradient(135deg, var(--edu-primary) 0%, #3b82f6 100%);
    color: white;
    padding: 0.2em 0.6em;
    border-radius: 12px;
    font-size: var(--text-xs);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-block;
    text-decoration: none;
    box-shadow: var(--shadow-sm);
}

.enhanced-citation:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.academic-citation {
    background: rgba(37, 99, 235, 0.1);
    color: var(--edu-primary);
    padding: 0.2em 0.5em;
    border-radius: 8px;
    font-size: var(--text-xs);
    font-weight: 600;
    border: 1px solid rgba(37, 99, 235, 0.3);
}

/* Citation Popup */
.citation-popup {
    background: white;
    border-radius: 12px;
    box-shadow: var(--shadow-xl);
    border: 1px solid #e2e8f0;
    min-width: 300px;
    animation: fadeInUp 0.3s ease;
}

.citation-content {
    padding: var(--space-4);
    position: relative;
}

.citation-content h4 {
    margin: 0 0 var(--space-3) 0;
    color: var(--edu-primary);
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.citation-content p {
    margin: var(--space-2) 0;
    font-size: var(--text-sm);
}

.close-citation {
    position: absolute;
    top: var(--space-2);
    right: var(--space-2);
    background: none;
    border: none;
    color: var(--chat-text-secondary-light);
    cursor: pointer;
    padding: var(--space-1);
    border-radius: 4px;
    transition: all 0.2s ease;
}

.close-citation:hover {
    background: #f1f5f9;
    color: var(--edu-primary);
}

/* Professional Collapsible Sections */
.professional-collapsible-section {
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    margin: var(--space-6) 0;
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    transition: all 0.3s ease;
}

.professional-collapsible-section:hover {
    box-shadow: var(--shadow-md);
}

.professional-collapsible-header {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    padding: var(--space-4) var(--space-6);
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all 0.2s ease;
    border-bottom: 1px solid #e2e8f0;
}

.professional-collapsible-header:hover {
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
}

.header-content {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    font-weight: 600;
    color: var(--edu-primary);
}

.section-icon {
    color: var(--edu-secondary);
}

.header-controls {
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.expand-hint {
    font-size: var(--text-sm);
    color: var(--chat-text-secondary-light);
    font-weight: 500;
}

.professional-collapsible-content {
    padding: var(--space-6);
    background: white;
    animation: slideDown 0.3s ease;
}

/* Professional Copy Buttons */
.professional-copy-btn {
    position: absolute;
    top: var(--space-2);
    right: var(--space-2);
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0.9) 100%);
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: var(--space-1);
    opacity: 0;
    transition: all 0.3s ease;
    padding: var(--space-2) var(--space-3);
    font-size: var(--text-xs);
    font-weight: 600;
}

pre:hover .professional-copy-btn,
.diagram:hover .professional-copy-btn {
    opacity: 1;
}

.professional-copy-btn:hover {
    background: linear-gradient(135deg, var(--edu-primary) 0%, #3b82f6 100%);
    transform: translateY(-1px);
}

.professional-copy-btn.copied {
    background: linear-gradient(135deg, var(--edu-success) 0%, #34d399 100%);
}

.copy-text {
    font-size: var(--text-xs);
}

/* Professional Table Wrapper */
.professional-table-wrapper {
    margin: var(--space-6) 0;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: var(--shadow-md);
    border: 1px solid #e2e8f0;
}

.table-controls {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    padding: var(--space-3) var(--space-4);
    display: flex;
    gap: var(--space-2);
    border-bottom: 1px solid #e2e8f0;
}

.table-btn {
    padding: var(--space-1) var(--space-2);
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    cursor: pointer;
    color: var(--chat-text-secondary-light);
    transition: all 0.2s ease;
    font-size: var(--text-sm);
}

.table-btn:hover {
    background: var(--edu-primary);
    color: white;
    transform: translateY(-1px);
}

/* Summary Points */
.summary-point {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    margin: var(--space-2) 0;
    padding: var(--space-2) var(--space-3);
    background: linear-gradient(135deg, rgba(5, 150, 105, 0.1) 0%, rgba(5, 150, 105, 0.05) 100%);
    border-radius: 8px;
    border-left: 3px solid var(--edu-success);
}

.summary-point i {
    color: var(--edu-success);
}

/* Measurement Highlights */
.measurement-highlight {
    background: linear-gradient(120deg, rgba(245, 158, 11, 0.15) 0%, rgba(245, 158, 11, 0.08) 100%);
    color: var(--edu-warning);
    padding: 0.1em 0.3em;
    border-radius: 4px;
    font-weight: 600;
    border: 1px solid rgba(245, 158, 11, 0.3);
}

/* Quality Indicator */
.quality-indicator {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-3) var(--space-4);
    margin-top: var(--space-4);
    border-radius: 8px;
    font-size: var(--text-sm);
    font-weight: 600;
}

.quality-indicator.excellent-quality {
    background: linear-gradient(135deg, rgba(5, 150, 105, 0.1) 0%, rgba(5, 150, 105, 0.05) 100%);
    color: var(--edu-success);
    border: 1px solid rgba(5, 150, 105, 0.3);
}

.quality-indicator.good-quality {
    background: linear-gradient(135deg, rgba(37, 99, 235, 0.1) 0%, rgba(37, 99, 235, 0.05) 100%);
    color: var(--edu-primary);
    border: 1px solid rgba(37, 99, 235, 0.3);
}

.quality-indicator.fair-quality {
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, rgba(245, 158, 11, 0.05) 100%);
    color: var(--edu-warning);
    border: 1px solid rgba(245, 158, 11, 0.3);
}

.quality-indicator.needs-improvement {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.1) 0%, rgba(239, 68, 68, 0.05) 100%);
    color: var(--edu-error);
    border: 1px solid rgba(239, 68, 68, 0.3);
}

/* Diagram Modal */
.diagram-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    animation: fadeIn 0.3s ease;
}

.diagram-modal-content {
    background: white;
    border-radius: 16px;
    max-width: 90vw;
    max-height: 90vh;
    overflow: auto;
    box-shadow: var(--shadow-xl);
}

.diagram-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-4) var(--space-6);
    border-bottom: 1px solid #e2e8f0;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.diagram-modal-header h3 {
    margin: 0;
    color: var(--edu-primary);
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.close-modal {
    background: none;
    border: none;
    color: var(--chat-text-secondary-light);
    cursor: pointer;
    padding: var(--space-2);
    border-radius: 6px;
    transition: all 0.2s ease;
}

.close-modal:hover {
    background: #f1f5f9;
    color: var(--edu-primary);
}

.diagram-modal-body {
    padding: var(--space-6);
}

/* Print Styles */
@media print {
    .enhanced-message-content,
    .professional-message-content {
        background: white !important;
        border: 1px solid #000 !important;
        box-shadow: none !important;
    }

    .diagram-controls,
    .copy-code-btn,
    .professional-copy-btn,
    .collapsible-icon,
    .table-controls,
    .quality-indicator {
        display: none !important;
    }

    .collapsible-content,
    .professional-collapsible-content {
        display: block !important;
    }

    .enhanced-message h1,
    .enhanced-message h2,
    .enhanced-message h3,
    .professional-message h1,
    .professional-message h2,
    .professional-message h3 {
        color: #000 !important;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .enhanced-message-content {
        padding: var(--space-4);
    }

    .enhanced-message h1 {
        font-size: var(--text-2xl);
    }

    .enhanced-message h2 {
        font-size: var(--text-xl);
    }

    .enhanced-message h3 {
        font-size: var(--text-lg);
    }

    .enhanced-message table {
        font-size: var(--text-sm);
    }

    .enhanced-message th,
    .enhanced-message td {
        padding: var(--space-2) var(--space-3);
    }

    .diagram-container {
        margin: var(--space-4) 0;
    }

    .diagram-header {
        padding: var(--space-2) var(--space-3);
    }

    .diagram-controls {
        gap: var(--space-1);
    }

    .diagram-btn {
        width: 28px;
        height: 28px;
    }

    .message-metadata {
        flex-wrap: wrap;
        gap: var(--space-2);
    }
}
