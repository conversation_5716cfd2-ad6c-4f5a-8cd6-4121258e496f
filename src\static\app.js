// Futuristic Educational RAG App - JavaScript

// DOM Elements - Will be initialized after DOM loads
let getStartedBtn, startChatBtn, floatingChatBtn, watchDemoBtn, chatInterface, closeChatBtn;
let chatSidebar, toggleSidebarBtn, newChatBtn, chatSearchInput, chatHistoryList, clearHistoryBtn;
let themeToggleBtn, minimizeChatBtn, maximizeChatBtn;
let chatMessages, queryForm, queryInput;
let attachBtn, fileInput, filePreview, voiceBtn, voiceRecording, emojiBtn, emojiPicker, emojiGrid;

// Global Variables
let currentTheme = localStorage.getItem('theme') || 'light';
let isRecording = false;
let mediaRecorder = null;
let recordingTimer = null;
let recordingStartTime = 0;
let chatHistory = JSON.parse(localStorage.getItem('chat-history')) || [];
let currentChatId = null;
let sidebarCollapsed = false;

// Message Templates
const createUserMessage = (text) => `
    <div class="flex justify-end mb-4">
        <div class="bg-blue-600 text-white rounded-lg py-2 px-4 max-w-[70%]">
            ${escapeHtml(text)}
        </div>
    </div>
`;

const createAssistantMessage = (response) => `
    <div class="flex justify-start mb-4">
        <div class="bg-white rounded-lg py-2 px-4 max-w-[70%] shadow">
            <div class="prose">
                ${escapeHtml(response.answer)}
            </div>
            ${response.diagram_data ? createDiagramSection(response.diagram_data, response.diagram_type) : ''}
            ${response.sources.length > 0 ? createSourcesSection(response.sources) : ''}
            <div class="mt-2 text-sm text-gray-500">
                Confidence: ${(response.confidence * 100).toFixed(1)}%
            </div>
        </div>
    </div>
`;

const createSourcesSection = (sources) => `
    <div class="mt-2 text-sm text-gray-600">
        <div class="font-semibold">Sources:</div>
        <ul class="list-disc list-inside">
            ${sources.map(source => `
                <li>${escapeHtml(source.source)} (Relevance: ${(source.score * 100).toFixed(1)}%)</li>
            `).join('')}
        </ul>
    </div>
`;

const createDiagramSection = (diagramData, diagramType) => {
    const diagramId = 'diagram-' + Date.now();
    setTimeout(() => {
        const diagramContainer = document.getElementById(diagramId);
        if (diagramContainer) {
            const mermaidCode = window.chatController.generateMermaidCode(diagramType, diagramData);
            diagramContainer.innerHTML = mermaidCode;
            mermaid.init(undefined, diagramContainer);
        }
    }, 0);
    
    return `
        <div class="mt-4 diagram-wrapper">
            <div id="${diagramId}" class="mermaid">
                Loading diagram...
            </div>
        </div>
    `;
};

const createStatusIndicator = (name, status) => `
    <div class="text-center">
        <div class="text-sm font-medium">${escapeHtml(name)}</div>
        <div class="mt-1">
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                status ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
            }">
                ${status ? 'Healthy' : 'Unhealthy'}
            </span>
        </div>
    </div>
`;

// Theme Management System
class ThemeManager {
    constructor() {
        this.currentTheme = localStorage.getItem('theme') || 'light';
        this.init();
    }

    init() {
        this.applyTheme(this.currentTheme);
        this.setupThemeToggle();
    }

    applyTheme(theme) {
        document.documentElement.setAttribute('data-theme', theme);
        this.currentTheme = theme;
        localStorage.setItem('theme', theme);

        // Update theme toggle icon
        if (themeToggleBtn) {
            const icon = themeToggleBtn.querySelector('i');
            if (theme === 'dark') {
                icon.className = 'fas fa-sun';
                themeToggleBtn.classList.add('dark-mode');
            } else {
                icon.className = 'fas fa-moon';
                themeToggleBtn.classList.remove('dark-mode');
            }
        }
    }

    toggleTheme() {
        const newTheme = this.currentTheme === 'light' ? 'dark' : 'light';
        this.applyTheme(newTheme);

        // Add transition effect
        document.body.style.transition = 'all 0.3s ease';
        setTimeout(() => {
            document.body.style.transition = '';
        }, 300);
    }

    setupThemeToggle() {
        if (themeToggleBtn) {
            themeToggleBtn.addEventListener('click', () => this.toggleTheme());
        }
    }
}

// Chat History Management
class ChatHistoryManager {
    constructor() {
        this.history = JSON.parse(localStorage.getItem('chat-history')) || [];
        this.currentChatId = null;
        this.init();
    }

    init() {
        this.renderHistory();
        this.setupEventListeners();
    }

    setupEventListeners() {
        if (newChatBtn) {
            newChatBtn.addEventListener('click', () => this.createNewChat());
        }

        if (clearHistoryBtn) {
            clearHistoryBtn.addEventListener('click', () => this.clearHistory());
        }

        if (chatSearchInput) {
            chatSearchInput.addEventListener('input', (e) => this.searchHistory(e.target.value));
        }
    }

    createNewChat() {
        const chatId = 'chat_' + Date.now();
        const newChat = {
            id: chatId,
            title: 'New Conversation',
            messages: [],
            timestamp: Date.now(),
            preview: 'Start a new conversation...'
        };

        this.history.unshift(newChat);
        this.currentChatId = chatId;
        this.saveHistory();
        this.renderHistory();
        this.clearMessages();

        // Add welcome message
        if (window.chatController) {
            window.chatController.addWelcomeMessage();
        }
    }

    saveHistory() {
        localStorage.setItem('chat-history', JSON.stringify(this.history));
    }

    clearHistory() {
        if (confirm('Are you sure you want to clear all chat history?')) {
            this.history = [];
            this.currentChatId = null;
            this.saveHistory();
            this.renderHistory();
            this.clearMessages();
        }
    }

    clearMessages() {
        if (chatMessages) {
            chatMessages.innerHTML = '';
        }
    }

    addMessageToHistory(type, content) {
        if (!this.currentChatId) {
            this.createNewChat();
        }

        const chat = this.history.find(c => c.id === this.currentChatId);
        if (chat) {
            chat.messages.push({
                type,
                content,
                timestamp: Date.now()
            });

            // Update chat title and preview
            if (type === 'user' && chat.title === 'New Conversation') {
                chat.title = content.substring(0, 30) + (content.length > 30 ? '...' : '');
            }

            if (type === 'user') {
                chat.preview = content.substring(0, 50) + (content.length > 50 ? '...' : '');
            }

            chat.timestamp = Date.now();
            this.saveHistory();
            this.renderHistory();
        }
    }

    loadChat(chatId) {
        const chat = this.history.find(c => c.id === chatId);
        if (chat) {
            this.currentChatId = chatId;
            this.clearMessages();

            // Render all messages
            chat.messages.forEach(msg => {
                if (window.chatController) {
                    window.chatController.addMessage(msg.type, msg.content, false);
                }
            });

            this.renderHistory();
        }
    }

    searchHistory(query) {
        const filtered = this.history.filter(chat =>
            chat.title.toLowerCase().includes(query.toLowerCase()) ||
            chat.preview.toLowerCase().includes(query.toLowerCase())
        );
        this.renderHistory(filtered);
    }

    renderHistory(historyToRender = null) {
        if (!chatHistoryList) return;

        const history = historyToRender || this.history;

        chatHistoryList.innerHTML = history.map(chat => `
            <div class="chat-history-item ${chat.id === this.currentChatId ? 'active' : ''}"
                 data-chat-id="${chat.id}">
                <div class="history-item-title">${this.escapeHtml(chat.title)}</div>
                <div class="history-item-preview">${this.escapeHtml(chat.preview)}</div>
                <div class="history-item-time">${this.formatTime(chat.timestamp)}</div>
            </div>
        `).join('');

        // Add click listeners
        chatHistoryList.querySelectorAll('.chat-history-item').forEach(item => {
            item.addEventListener('click', () => {
                const chatId = item.dataset.chatId;
                this.loadChat(chatId);
            });
        });
    }

    formatTime(timestamp) {
        const date = new Date(timestamp);
        const now = new Date();
        const diff = now - date;

        if (diff < 24 * 60 * 60 * 1000) {
            return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
        } else if (diff < 7 * 24 * 60 * 60 * 1000) {
            return date.toLocaleDateString([], { weekday: 'short' });
        } else {
            return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
        }
    }

    escapeHtml(unsafe) {
        if (!unsafe) return '';
        return unsafe
            .replace(/&/g, "&amp;")
            .replace(/</g, "&lt;")
            .replace(/>/g, "&gt;")
            .replace(/"/g, "&quot;")
            .replace(/'/g, "&#039;");
    }
}

// Utility Functions
function escapeHtml(unsafe) {
    return unsafe
        .replace(/&/g, "&amp;")
        .replace(/</g, "&lt;")
        .replace(/>/g, "&gt;")
        .replace(/"/g, "&quot;")
        .replace(/'/g, "&#039;");
}

function scrollToBottom() {
    if (chatMessages) {
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }
}

// File Attachment Manager
class FileAttachmentManager {
    constructor() {
        this.currentFile = null;
        this.init();
    }

    init() {
        this.setupEventListeners();
    }

    setupEventListeners() {
        if (attachBtn) {
            attachBtn.addEventListener('click', () => this.openFileDialog());
        }

        if (fileInput) {
            fileInput.addEventListener('change', (e) => this.handleFileSelect(e));
        }

        // Remove file button
        document.addEventListener('click', (e) => {
            if (e.target.closest('.remove-file-btn')) {
                this.removeFile();
            }
        });
    }

    openFileDialog() {
        if (fileInput) {
            fileInput.click();
        }
    }

    handleFileSelect(event) {
        const file = event.target.files[0];
        if (file) {
            this.currentFile = file;
            this.showFilePreview(file);
        }
    }

    showFilePreview(file) {
        if (!filePreview) return;

        const fileName = file.name;
        const fileSize = this.formatFileSize(file.size);

        filePreview.querySelector('.file-name').textContent = fileName;
        filePreview.querySelector('.file-size').textContent = fileSize;
        filePreview.classList.remove('hidden');
    }

    removeFile() {
        this.currentFile = null;
        if (filePreview) {
            filePreview.classList.add('hidden');
        }
        if (fileInput) {
            fileInput.value = '';
        }
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    getAttachedFile() {
        return this.currentFile;
    }
}

// Voice Recording Manager
class VoiceRecordingManager {
    constructor() {
        this.isRecording = false;
        this.mediaRecorder = null;
        this.recordingTimer = null;
        this.recordingStartTime = 0;
        this.audioChunks = [];
        this.init();
    }

    init() {
        this.setupEventListeners();
    }

    setupEventListeners() {
        if (voiceBtn) {
            voiceBtn.addEventListener('click', () => this.toggleRecording());
        }

        // Stop recording when clicking on recording indicator
        if (voiceRecording) {
            voiceRecording.addEventListener('click', () => this.stopRecording());
        }
    }

    async toggleRecording() {
        if (this.isRecording) {
            this.stopRecording();
        } else {
            await this.startRecording();
        }
    }

    async startRecording() {
        try {
            const stream = await navigator.mediaDevices.getUserMedia({ audio: true });

            this.mediaRecorder = new MediaRecorder(stream);
            this.audioChunks = [];

            this.mediaRecorder.ondataavailable = (event) => {
                this.audioChunks.push(event.data);
            };

            this.mediaRecorder.onstop = () => {
                this.processRecording();
            };

            this.mediaRecorder.start();
            this.isRecording = true;
            this.recordingStartTime = Date.now();

            this.showRecordingUI();
            this.startTimer();

        } catch (error) {
            console.error('Error accessing microphone:', error);
            alert('Unable to access microphone. Please check permissions.');
        }
    }

    stopRecording() {
        if (this.mediaRecorder && this.isRecording) {
            this.mediaRecorder.stop();
            this.mediaRecorder.stream.getTracks().forEach(track => track.stop());
            this.isRecording = false;
            this.hideRecordingUI();
            this.stopTimer();
        }
    }

    showRecordingUI() {
        if (voiceRecording) {
            voiceRecording.classList.remove('hidden');
        }
        if (voiceBtn) {
            voiceBtn.classList.add('recording');
        }
    }

    hideRecordingUI() {
        if (voiceRecording) {
            voiceRecording.classList.add('hidden');
        }
        if (voiceBtn) {
            voiceBtn.classList.remove('recording');
        }
    }

    startTimer() {
        this.recordingTimer = setInterval(() => {
            const elapsed = Date.now() - this.recordingStartTime;
            const minutes = Math.floor(elapsed / 60000);
            const seconds = Math.floor((elapsed % 60000) / 1000);
            const timeString = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

            const timerElement = voiceRecording?.querySelector('.recording-timer');
            if (timerElement) {
                timerElement.textContent = timeString;
            }
        }, 1000);
    }

    stopTimer() {
        if (this.recordingTimer) {
            clearInterval(this.recordingTimer);
            this.recordingTimer = null;
        }
    }

    processRecording() {
        const audioBlob = new Blob(this.audioChunks, { type: 'audio/wav' });

        // For now, we'll convert to text using Web Speech API
        // In a real implementation, you'd send this to a speech-to-text service
        this.convertSpeechToText(audioBlob);
    }

    convertSpeechToText(audioBlob) {
        // This is a simplified implementation
        // In production, you'd use a proper speech-to-text service
        const recognition = new (window.SpeechRecognition || window.webkitSpeechRecognition)();

        if (recognition) {
            recognition.continuous = false;
            recognition.interimResults = false;
            recognition.lang = 'en-US';

            recognition.onresult = (event) => {
                const transcript = event.results[0][0].transcript;
                if (queryInput) {
                    queryInput.value = transcript;
                    queryInput.focus();
                }
            };

            recognition.onerror = (event) => {
                console.error('Speech recognition error:', event.error);
            };

            // Note: This is a simplified approach
            // Real implementation would process the audioBlob
            recognition.start();
        } else {
            alert('Speech recognition not supported in this browser.');
        }
    }
}

// Emoji Picker Manager
class EmojiPickerManager {
    constructor() {
        this.isOpen = false;
        this.emojis = {
            smileys: ['😀', '😃', '😄', '😁', '😆', '😅', '🤣', '😂', '🙂', '🙃', '😉', '😊', '😇', '🥰', '😍', '🤩', '😘', '😗', '😚', '😙'],
            people: ['👋', '🤚', '🖐', '✋', '🖖', '👌', '🤏', '✌', '🤞', '🤟', '🤘', '🤙', '👈', '👉', '👆', '🖕', '👇', '☝', '👍', '👎'],
            nature: ['🌟', '⭐', '🌙', '☀', '⛅', '🌤', '⛈', '🌧', '❄', '☃', '⛄', '🌈', '🔥', '💧', '🌊', '🎄', '🌲', '🌳', '🌴', '🌱'],
            objects: ['📚', '📖', '📝', '✏', '🖊', '🖋', '🖌', '🖍', '📄', '📃', '📑', '📊', '📈', '📉', '📇', '🗃', '🗄', '🗂', '📂', '📁']
        };
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.renderEmojis('smileys');
    }

    setupEventListeners() {
        if (emojiBtn) {
            emojiBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.togglePicker();
            });
        }

        document.addEventListener('click', (e) => {
            if (!e.target.closest('#emoji-picker') && !e.target.closest('#emoji-btn')) {
                this.closePicker();
            }
        });

        document.addEventListener('click', (e) => {
            if (e.target.closest('.emoji-category')) {
                const category = e.target.closest('.emoji-category').dataset.category;
                this.switchCategory(category);
            }
        });

        document.addEventListener('click', (e) => {
            if (e.target.closest('.emoji-item')) {
                const emoji = e.target.closest('.emoji-item').textContent;
                this.insertEmoji(emoji);
            }
        });
    }

    togglePicker() {
        if (this.isOpen) {
            this.closePicker();
        } else {
            this.openPicker();
        }
    }

    openPicker() {
        if (emojiPicker) {
            emojiPicker.classList.remove('hidden');
            this.isOpen = true;
        }
    }

    closePicker() {
        if (emojiPicker) {
            emojiPicker.classList.add('hidden');
            this.isOpen = false;
        }
    }

    switchCategory(category) {
        document.querySelectorAll('.emoji-category').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-category="${category}"]`)?.classList.add('active');
        this.renderEmojis(category);
    }

    renderEmojis(category) {
        if (!emojiGrid || !this.emojis[category]) return;
        emojiGrid.innerHTML = this.emojis[category].map(emoji =>
            `<button class="emoji-item" type="button">${emoji}</button>`
        ).join('');
    }

    insertEmoji(emoji) {
        if (queryInput) {
            const cursorPos = queryInput.selectionStart;
            const textBefore = queryInput.value.substring(0, cursorPos);
            const textAfter = queryInput.value.substring(queryInput.selectionEnd);
            queryInput.value = textBefore + emoji + textAfter;
            queryInput.focus();
            queryInput.setSelectionRange(cursorPos + emoji.length, cursorPos + emoji.length);
        }
        this.closePicker();
    }
}

// Sidebar Manager
class SidebarManager {
    constructor() {
        this.isCollapsed = false;
        this.init();
    }

    init() {
        this.setupEventListeners();
    }

    setupEventListeners() {
        if (toggleSidebarBtn) {
            toggleSidebarBtn.addEventListener('click', () => this.toggleSidebar());
        }
    }

    toggleSidebar() {
        if (chatSidebar) {
            if (this.isCollapsed) {
                chatSidebar.classList.remove('collapsed');
                chatSidebar.classList.add('open');
            } else {
                chatSidebar.classList.add('collapsed');
                chatSidebar.classList.remove('open');
            }
            this.isCollapsed = !this.isCollapsed;
        }
    }
}

// Animation and Interaction Controllers
class AnimationController {
    constructor() {
        this.observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };
        this.init();
    }

    init() {
        this.setupIntersectionObserver();
        this.setupScrollAnimations();
        this.setupHoverEffects();
        this.setupCounterAnimations();
    }

    setupIntersectionObserver() {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('fade-in');
                    
                    // Trigger specific animations
                    if (entry.target.classList.contains('tech-card')) {
                        this.animateTechCard(entry.target);
                    }
                    if (entry.target.classList.contains('coverage-fill')) {
                        this.animateProgressBar(entry.target);
                    }
                }
            });
        }, this.observerOptions);

        // Observe elements
        document.querySelectorAll('.glass-card, .benefit-card, .tech-card, .feature-item').forEach(el => {
            observer.observe(el);
        });
    }

    setupScrollAnimations() {
        let lastScrollY = window.scrollY;
        
        window.addEventListener('scroll', () => {
            const currentScrollY = window.scrollY;
            
            // Parallax effect for hero section
            const heroSection = document.querySelector('.hero-section');
            if (heroSection) {
                const scrolled = window.pageYOffset;
                const parallax = scrolled * -0.2;
                heroSection.style.transform = `translateY(${parallax}px)`;
            }

            // Floating cards animation on scroll
            document.querySelectorAll('.floating-card').forEach((card, index) => {
                const offset = (window.scrollY * 0.1 * (index + 1));
                card.style.transform = `translateY(${offset}px)`;
            });

            lastScrollY = currentScrollY;
        });
    }

    setupHoverEffects() {
        // Enhanced hover effects for glass cards
        document.querySelectorAll('.glass-card').forEach(card => {
            card.addEventListener('mouseenter', () => {
                card.style.transform = 'translateY(-12px) scale(1.02)';
                card.style.boxShadow = '0 25px 50px 0 rgba(31, 38, 135, 0.5)';
            });

            card.addEventListener('mouseleave', () => {
                card.style.transform = 'translateY(0) scale(1)';
                card.style.boxShadow = '0 8px 32px 0 rgba(31, 38, 135, 0.37)';
            });
        });

        // Button hover effects
        document.querySelectorAll('.btn-primary, .btn-secondary, .btn-accent, .btn-success').forEach(btn => {
            btn.addEventListener('mouseenter', () => {
                btn.style.transform = 'translateY(-3px) scale(1.02)';
                this.createRippleEffect(btn);
            });

            btn.addEventListener('mouseleave', () => {
                btn.style.transform = 'translateY(0) scale(1)';
            });
        });
    }

    setupCounterAnimations() {
        const counters = document.querySelectorAll('.tech-stat, .stat-value');
        
        counters.forEach(counter => {
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        this.animateCounter(entry.target);
                        observer.unobserve(entry.target);
                    }
                });
            });
            
            observer.observe(counter);
        });
    }

    animateCounter(element) {
        const text = element.textContent;
        const number = parseFloat(text.replace(/[^\d.]/g, ''));
        
        if (isNaN(number)) return;
        
        const suffix = text.replace(number.toString(), '');
        const duration = 2000;
        const steps = 50;
        const increment = number / steps;
        let current = 0;
        
        const timer = setInterval(() => {
            current += increment;
            if (current >= number) {
                current = number;
                clearInterval(timer);
            }
            
            element.textContent = Math.floor(current) + suffix;
        }, duration / steps);
    }

    animateTechCard(card) {
        const icon = card.querySelector('.tech-icon');
        if (icon) {
            icon.style.animation = 'pulse 2s infinite';
        }
    }

    animateProgressBar(bar) {
        const width = bar.style.width || '0%';
        bar.style.width = '0%';
        
        setTimeout(() => {
            bar.style.transition = 'width 2s ease-out';
            bar.style.width = width;
        }, 200);
    }

    createRippleEffect(element) {
        const ripple = document.createElement('span');
        ripple.classList.add('ripple-effect');
        ripple.style.cssText = `
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.4);
            transform: scale(0);
            animation: ripple 0.6s linear;
            pointer-events: none;
            width: 20px;
            height: 20px;
            left: 50%;
            top: 50%;
            margin-left: -10px;
            margin-top: -10px;
        `;
        
        element.style.position = 'relative';
        element.style.overflow = 'hidden';
        element.appendChild(ripple);
        
        setTimeout(() => {
            ripple.remove();
        }, 600);
    }
}

// Modern Chat Interface Controller
class ChatController {
    constructor() {
        this.initializeElements();
        this.setupEventListeners();
        this.setupTheme();
        this.initializeApp();
    }

    initializeElements() {
        // Main chat elements
        this.chatInterface = document.getElementById('chat-interface');
        this.messageContainer = document.getElementById('chat-messages');
        this.queryInput = document.getElementById('query-input');
        this.sendButton = document.getElementById('send-button');
        
        // Control buttons
        this.closeButton = document.getElementById('close-chat');
        this.minimizeButton = document.getElementById('minimize-chat-btn');
        this.themeToggleButton = document.getElementById('theme-toggle-btn');
        
        // Feature buttons
        this.attachButton = document.getElementById('attach-btn');
        this.voiceButton = document.getElementById('voice-btn');
        this.emojiButton = document.getElementById('emoji-btn');
        
        // Additional UI elements
        this.filePreview = document.createElement('div');
        this.filePreview.className = 'file-preview hidden';
        
        this.voiceRecording = document.createElement('div');
        this.voiceRecording.className = 'voice-recording hidden';
        
        this.emojiPicker = document.createElement('div');
        this.emojiPicker.className = 'emoji-picker hidden';
        
        // Append UI elements to chat input container
        const inputContainer = document.querySelector('.chat-input-container');
        if (inputContainer) {
            inputContainer.appendChild(this.filePreview);
            inputContainer.appendChild(this.voiceRecording);
            inputContainer.appendChild(this.emojiPicker);
        }
    }

    setupEventListeners() {
        // Chat visibility controls
        const startChatBtn = document.getElementById('get-started-btn');
        if (startChatBtn) {
            startChatBtn.addEventListener('click', () => this.showChat());
        }
        
        this.closeButton?.addEventListener('click', () => this.hideChat());
        this.minimizeButton?.addEventListener('click', () => this.minimizeChat());
        
        // Message input handling
        this.queryInput?.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });
        
        this.queryInput?.addEventListener('input', (e) => {
            // Auto-resize textarea
            e.target.style.height = 'auto';
            e.target.style.height = Math.min(e.target.scrollHeight, 120) + 'px';
            
            // Enable/disable send button
            if (this.sendButton) {
                this.sendButton.disabled = !e.target.value.trim();
            }
        });
        
        this.sendButton?.addEventListener('click', () => this.sendMessage());
        
        // Feature buttons
        this.attachButton?.addEventListener('click', () => this.toggleFileUpload());
        this.voiceButton?.addEventListener('click', () => this.toggleVoiceRecording());
        this.emojiButton?.addEventListener('click', () => this.toggleEmojiPicker());
        
        // Theme toggle
        this.themeToggleButton?.addEventListener('click', () => this.toggleTheme());
        
        // Global keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.hideChat();
                this.hideAllPopups();
            }
        });
        
        // Click outside to close popups
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.file-preview') && 
                !e.target.closest('.voice-recording') && 
                !e.target.closest('.emoji-picker') && 
                !e.target.closest('#attach-btn') && 
                !e.target.closest('#voice-btn') && 
                !e.target.closest('#emoji-btn')) {
                this.hideAllPopups();
            }
        });
    }

    initializeApp() {
        // Set initial theme
        document.documentElement.setAttribute('data-theme', currentTheme);
        
        // Show body after initialization
        setTimeout(() => {
            document.body.classList.add('loaded');
        }, 100);
    }

    showChat() {
        if (this.chatInterface) {
            this.chatInterface.classList.remove('hidden');
            setTimeout(() => {
                this.chatInterface.classList.add('active');
                if (this.queryInput) {
                    this.queryInput.focus();
                }
            }, 50);
            this.addWelcomeMessage();
        }
    }

    hideChat() {
        if (this.chatInterface) {
            this.chatInterface.classList.remove('active');
            setTimeout(() => {
                this.chatInterface.classList.add('hidden');
            }, 300);
            this.hideAllPopups();
        }
    }

    minimizeChat() {
        if (this.chatInterface) {
            this.chatInterface.classList.add('minimized');
            this.hideAllPopups();
        }
    }

    maximizeChat() {
        if (this.chatInterface) {
            this.chatInterface.classList.remove('minimized');
        }
    }

    async sendMessage() {
        const query = this.queryInput?.value.trim();
        if (!query) return;

        // Add user message
        this.addMessage('user', query);
        
        // Clear input and reset height
        if (this.queryInput) {
            this.queryInput.value = '';
            this.queryInput.style.height = 'auto';
        }
        if (this.sendButton) {
            this.sendButton.disabled = true;
        }

        // Show typing indicator
        this.showTypingIndicator();

        try {
            // Make API request
            const response = await fetch('/query', {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                body: `query=${encodeURIComponent(query)}`
            });

            if (!response.ok) throw new Error('Failed to get response');

            const data = await response.json();

            // Hide typing indicator and add enhanced AI response
            this.hideTypingIndicator();

            // Detect subject from classification or sources
            const subject = this.detectSubject(data);

            this.addMessage('assistant', data.answer || 'Sorry, I couldn\'t generate a response. Please try again.', {
                confidence: data.confidence,
                processingTime: data.processing_time,
                subject: subject,
                sources: data.sources || [],
                questionType: data.question_type
            });

        } catch (error) {
            console.error('Error:', error);
            this.hideTypingIndicator();
            this.addMessage('assistant', 'Sorry, I encountered an error. Please try again.');
        }
    }

    addMessage(type, content, options = {}) {
        if (!this.messageContainer) return;

        if (type === 'user') {
            // Simple user message
            const messageElement = document.createElement('div');
            messageElement.className = `message ${type}-message`;

            const bubbleElement = document.createElement('div');
            bubbleElement.className = 'message-bubble';
            bubbleElement.textContent = content;

            messageElement.appendChild(bubbleElement);
            this.messageContainer.appendChild(messageElement);
        } else if (type === 'assistant') {
            // Enhanced assistant message with rich formatting
            this.addEnhancedAssistantMessage(content, options);
        }

        // Scroll to bottom with smooth animation
        this.messageContainer.scrollTo({
            top: this.messageContainer.scrollHeight,
            behavior: 'smooth'
        });
    }

    addEnhancedAssistantMessage(content, options = {}) {
        if (!this.messageContainer) return;

        const messageElement = document.createElement('div');
        messageElement.className = 'message assistant-message';

        // Use enhanced formatter if available
        if (window.enhancedFormatter) {
            const formattedMessage = window.enhancedFormatter.formatMessage(content, {
                showMetadata: true,
                confidence: options.confidence,
                processingTime: options.processingTime,
                subject: options.subject || 'general'
            });

            messageElement.appendChild(formattedMessage);

            // Generate and add diagram if content is suitable
            if (window.visualContentGenerator && options.subject) {
                const diagram = window.visualContentGenerator.generateDiagram(content, options.subject);
                if (diagram) {
                    messageElement.appendChild(diagram);
                }
            }
        } else {
            // Fallback to simple formatting
            const bubbleElement = document.createElement('div');
            bubbleElement.className = 'message-bubble';
            bubbleElement.innerHTML = this.formatBasicMarkdown(content);
            messageElement.appendChild(bubbleElement);
        }

        this.messageContainer.appendChild(messageElement);
    }

    formatBasicMarkdown(text) {
        // Basic markdown formatting as fallback
        return text
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/`(.*?)`/g, '<code>$1</code>')
            .replace(/\n/g, '<br>');
    }

    detectSubject(responseData) {
        // Detect subject from sources or classification
        if (responseData.sources && responseData.sources.length > 0) {
            const source = responseData.sources[0];
            if (source.subject) {
                return source.subject.toLowerCase();
            }

            // Detect from source name
            const sourceName = source.source.toLowerCase();
            if (sourceName.includes('geography') || sourceName.includes('contemporary india')) {
                return 'geography';
            } else if (sourceName.includes('history') || sourceName.includes('contemporary world')) {
                return 'history';
            } else if (sourceName.includes('economics') || sourceName.includes('economic development')) {
                return 'economics';
            } else if (sourceName.includes('political') || sourceName.includes('democratic politics')) {
                return 'politics';
            }
        }

        // Detect from classification if available
        if (responseData.classification && responseData.classification.subject) {
            return responseData.classification.subject.toLowerCase();
        }

        // Fallback detection from answer content
        const answer = responseData.answer.toLowerCase();
        const subjectKeywords = {
            history: ['revolution', 'war', 'independence', 'empire', 'dynasty', 'battle', 'treaty'],
            geography: ['mountain', 'river', 'climate', 'monsoon', 'vegetation', 'plateau', 'desert'],
            economics: ['economy', 'gdp', 'inflation', 'market', 'trade', 'industry', 'agriculture'],
            politics: ['democracy', 'government', 'constitution', 'parliament', 'election', 'rights']
        };

        let maxScore = 0;
        let detectedSubject = 'general';

        Object.keys(subjectKeywords).forEach(subject => {
            const keywords = subjectKeywords[subject];
            const score = keywords.reduce((count, keyword) => {
                return count + (answer.includes(keyword) ? 1 : 0);
            }, 0);

            if (score > maxScore) {
                maxScore = score;
                detectedSubject = subject;
            }
        });

        return detectedSubject;
    }

    showTypingIndicator() {
        if (!this.messageContainer) return;

        const indicator = document.createElement('div');
        indicator.className = 'message assistant-message typing-indicator';
        indicator.innerHTML = `
            <div class="message-bubble">
                <div class="loading-dots">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>
        `;
        this.messageContainer.appendChild(indicator);
        this.messageContainer.scrollTo({
            top: this.messageContainer.scrollHeight,
            behavior: 'smooth'
        });
    }

    hideTypingIndicator() {
        const indicator = this.messageContainer?.querySelector('.typing-indicator');
        if (indicator) {
            indicator.remove();
        }
    }

    toggleFileUpload() {
        if (!this.filePreview) return;

        const isHidden = this.filePreview.classList.contains('hidden');
        this.hideAllPopups();
        
        if (isHidden) {
            this.filePreview.innerHTML = `
                <div class="file-preview-info">
                    <div class="file-preview-icon">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <div class="file-preview-details">
                        <div class="file-preview-name">Click to attach a file</div>
                        <div class="file-preview-size">Maximum size: 10MB</div>
                    </div>
                </div>
                <button class="file-preview-remove">
                    <i class="fas fa-times"></i>
                </button>
            `;
            
            // Add event listener for remove button
            this.filePreview.querySelector('.file-preview-remove').addEventListener('click', () => {
                this.hideAllPopups();
            });
            
            this.filePreview.classList.remove('hidden');
        }
    }

    toggleVoiceRecording() {
        if (!this.voiceRecording) return;

        const isHidden = this.voiceRecording.classList.contains('hidden');
        this.hideAllPopups();
        
        if (isHidden) {
            if (!isRecording) {
                this.startRecording();
            } else {
                this.stopRecording();
            }
        }
    }

    async startRecording() {
        try {
            const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
            mediaRecorder = new MediaRecorder(stream);
            isRecording = true;
            
            this.voiceRecording.innerHTML = `
                <div class="recording-indicator">
                    <div class="recording-dot"></div>
                    <div class="recording-timer">00:00</div>
                </div>
                <div class="recording-controls">
                    <button class="recording-btn recording-stop">
                        <i class="fas fa-stop"></i>
                    </button>
                    <button class="recording-btn recording-cancel">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;
            this.voiceRecording.classList.remove('hidden');
            
            // Setup recording timer
            recordingStartTime = Date.now();
            recordingTimer = setInterval(() => {
                const duration = Date.now() - recordingStartTime;
                const seconds = Math.floor((duration / 1000) % 60);
                const minutes = Math.floor((duration / (1000 * 60)) % 60);
                const timerElement = this.voiceRecording.querySelector('.recording-timer');
                if (timerElement) {
                    timerElement.textContent = 
                        `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                }
            }, 1000);
            
            // Setup recording controls
            this.voiceRecording.querySelector('.recording-stop')?.addEventListener('click', () => this.stopRecording());
            this.voiceRecording.querySelector('.recording-cancel')?.addEventListener('click', () => this.cancelRecording());
            
            mediaRecorder.start();
        } catch (error) {
            console.error('Error accessing microphone:', error);
            this.addMessage('assistant', 'Unable to access microphone. Please check your permissions.');
        }
    }

    stopRecording() {
        if (mediaRecorder && isRecording) {
            mediaRecorder.stop();
            isRecording = false;
            clearInterval(recordingTimer);
            
            mediaRecorder.ondataavailable = (e) => {
                const audioBlob = new Blob([e.data], { type: 'audio/webm' });
                const audioUrl = URL.createObjectURL(audioBlob);
                this.addMessage('user', `🎤 Audio Message - ${new Date().toLocaleTimeString()}`);
                // Here you would typically send the audio file to your server
            };
            
            this.hideAllPopups();
        }
    }

    cancelRecording() {
        if (mediaRecorder && isRecording) {
            mediaRecorder.stop();
            isRecording = false;
            clearInterval(recordingTimer);
            this.hideAllPopups();
        }
    }

    toggleEmojiPicker() {
        if (!this.emojiPicker) return;

        const isHidden = this.emojiPicker.classList.contains('hidden');
        this.hideAllPopups();
        
        if (isHidden) {
            // Initialize emoji picker if not already done
            if (!this.emojiPicker.children.length) {
                this.initializeEmojiPicker();
            }
            this.emojiPicker.classList.remove('hidden');
        }
    }

    initializeEmojiPicker() {
        const categories = ['😀', '❤️', '🌍', '🐶', '🍔', '⚽', '💡', '🎵'];
        const emojis = {
            '😀': ['😀', '😃', '😄', '😁', '😅', '😂', '🤣', '😊', '😇', '🙂', '🙃', '😉', '😌', '😍', '🥰', '😘'],
            '❤️': ['❤️', '🧡', '💛', '💚', '💙', '💜', '🤎', '🖤', '🤍', '💔', '❣️', '💕', '💞', '💓', '💗', '💖'],
            '🌍': ['🌍', '🌎', '🌏', '🌞', '🌙', '⭐', '☁️', '🌈', '🔥', '💧', '🌊', '⚡', '❄️', '☀️', '🌤️', '⛅'],
            '🐶': ['🐶', '🐱', '🐭', '🐹', '🐰', '🦊', '🐻', '🐼', '🐨', '🐯', '🦁', '🐮', '🐷', '🐸', '🐵', '🐔'],
            '🍔': ['🍔', '🍕', '🌭', '🍿', '🍩', '🍪', '🍫', '🍦', '🍰', '🎂', '🍎', '🍌', '🍓', '🥑', '🥕', '🌽'],
            '⚽': ['⚽', '🏀', '🏈', '⚾', '🎾', '🏐', '🏉', '🎱', '🏓', '🏸', '🥅', '🏒', '🏑', '🥍', '🏏', '⛳'],
            '💡': ['💡', '💭', '💬', '📚', '✏️', '📝', '🔍', '🎯', '📊', '📈', '📉', '🔬', '🧪', '🧬', '🔭', '📡'],
            '🎵': ['🎵', '🎶', '🎸', '🎹', '🎺', '🎻', '🥁', '🎤', '🎧', '📻', '🎼', '🎹', '🎸', '🎺', '🎷', '🪕']
        };

        this.emojiPicker.innerHTML = `
            <div class="emoji-categories">
                ${categories.map((cat, index) => `
                    <button class="emoji-category ${index === 0 ? 'active' : ''}" data-category="${cat}">${cat}</button>
                `).join('')}
            </div>
            <div class="emoji-grid"></div>
        `;

        const grid = this.emojiPicker.querySelector('.emoji-grid');
        const updateGrid = (category) => {
            grid.innerHTML = emojis[category].map(emoji => `
                <button class="emoji-item" data-emoji="${emoji}">${emoji}</button>
            `).join('');
        };

        // Set initial category
        updateGrid(categories[0]);

        // Setup category switching
        this.emojiPicker.querySelectorAll('.emoji-category').forEach(btn => {
            btn.addEventListener('click', () => {
                this.emojiPicker.querySelectorAll('.emoji-category').forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                updateGrid(btn.dataset.category);
            });
        });

        // Setup emoji selection
        grid.addEventListener('click', (e) => {
            const emojiBtn = e.target.closest('.emoji-item');
            if (emojiBtn && this.queryInput) {
                const emoji = emojiBtn.dataset.emoji;
                const start = this.queryInput.selectionStart;
                const end = this.queryInput.selectionEnd;
                const text = this.queryInput.value;
                this.queryInput.value = text.substring(0, start) + emoji + text.substring(end);
                this.queryInput.focus();
                this.queryInput.selectionStart = this.queryInput.selectionEnd = start + emoji.length;
                
                // Trigger input event to update send button state
                this.queryInput.dispatchEvent(new Event('input'));
            }
        });
    }

    hideAllPopups() {
        this.filePreview?.classList.add('hidden');
        this.voiceRecording?.classList.add('hidden');
        this.emojiPicker?.classList.add('hidden');
    }

    setupTheme() {
        document.documentElement.setAttribute('data-theme', currentTheme);
        if (this.themeToggleButton) {
            const icon = this.themeToggleButton.querySelector('i');
            if (icon) {
                icon.className = `fas fa-${currentTheme === 'light' ? 'moon' : 'sun'}`;
            }
        }
    }

    toggleTheme() {
        currentTheme = currentTheme === 'light' ? 'dark' : 'light';
        localStorage.setItem('theme', currentTheme);
        this.setupTheme();
        
        // Add smooth transition effect
        document.documentElement.style.transition = 'all 0.3s ease';
        setTimeout(() => {
            document.documentElement.style.transition = '';
        }, 300);
    }

    addWelcomeMessage() {
        this.addMessage('assistant', 'Hello! 👋 I\'m your AI Study Assistant for CBSE Class 9. I can help you with History, Geography, Political Science, and Economics. What would you like to learn today?');
    }
}

// CTA Buttons Controller
class CTAController {
    constructor() {
        this.chatController = null;
        this.init();
    }

    init() {
        this.setupCTAButtons();
        this.chatController = new ChatController();
    }

    setupCTAButtons() {
        // Get Started Button (Start Chat)
        const getStartedBtn = document.getElementById('get-started-btn');
        if (getStartedBtn) {
            getStartedBtn.addEventListener('click', () => {
                if (this.chatController) {
                    this.chatController.showChat();
                }
            });
        }

        // Start Chat Button in Demo Section
        const startChatBtn = document.getElementById('start-chat-btn');
        if (startChatBtn) {
            startChatBtn.addEventListener('click', () => {
                if (this.chatController) {
                    this.chatController.showChat();
                }
            });
        }

        // Book Counselling
        const bookingBtn = document.querySelector('.cta-btn:first-child');
        if (bookingBtn) {
            bookingBtn.addEventListener('click', () => {
                this.handleBooking();
            });
        }

        // Download App
        const downloadBtn = document.querySelector('.cta-btn:nth-child(2)');
        if (downloadBtn) {
            downloadBtn.addEventListener('click', () => {
                this.handleDownload();
            });
        }

        // WhatsApp
        const whatsappBtn = document.querySelector('.cta-btn:last-child');
        if (whatsappBtn) {
            whatsappBtn.addEventListener('click', () => {
                this.handleWhatsApp();
            });
        }
    }

    handleBooking() {
        window.open('https://calendly.com/your-booking-link', '_blank');
    }

    handleDownload() {
        window.open('https://apps.apple.com/your-app-link', '_blank');
    }

    handleWhatsApp() {
        const message = encodeURIComponent('Hi! I\'m interested in learning more about the Educational RAG AI App.');
        window.open(`https://wa.me/1234567890?text=${message}`, '_blank');
    }
}

// Accessibility Controller
class AccessibilityController {
    constructor() {
        this.init();
    }

    init() {
        this.setupKeyboardNavigation();
        this.setupAriaLabels();
        this.setupFocusManagement();
    }

    setupKeyboardNavigation() {
        document.addEventListener('keydown', (e) => {
            // Escape key closes chat
            if (e.key === 'Escape' && chatInterface && !chatInterface.classList.contains('hidden')) {
                chatController.closeChat();
            }
            
            // Enter key on buttons
            if (e.key === 'Enter' && e.target.classList.contains('btn-primary')) {
                e.target.click();
            }
        });
    }

    setupAriaLabels() {
        // Add ARIA labels for better screen reader support
        const buttons = document.querySelectorAll('button');
        buttons.forEach(btn => {
            if (!btn.getAttribute('aria-label')) {
                btn.setAttribute('aria-label', btn.textContent.trim());
            }
        });
        
        // Chat interface
        if (chatInterface) {
            chatInterface.setAttribute('role', 'dialog');
            chatInterface.setAttribute('aria-modal', 'true');
            chatInterface.setAttribute('aria-labelledby', 'chat-title');
        }
    }

    setupFocusManagement() {
        const focusableElements = 'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])';
        
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Tab' && chatInterface && !chatInterface.classList.contains('hidden')) {
                const focusable = chatInterface.querySelectorAll(focusableElements);
                const firstFocusable = focusable[0];
                const lastFocusable = focusable[focusable.length - 1];
                
                if (e.shiftKey) {
                    if (document.activeElement === firstFocusable) {
                        lastFocusable.focus();
                        e.preventDefault();
                    }
                } else {
                    if (document.activeElement === lastFocusable) {
                        firstFocusable.focus();
                        e.preventDefault();
                    }
                }
            }
        });
    }
}

// Initialize DOM Elements
function initializeDOMElements() {
    // Critical Elements - These must exist for the app to function
    const criticalElements = {
        'get-started-btn': document.getElementById('get-started-btn'),
        'chat-interface': document.getElementById('chat-interface'),
        'chat-messages': document.getElementById('chat-messages'),
        'query-input': document.getElementById('query-input'),
        'theme-toggle-btn': document.getElementById('theme-toggle-btn')
    };

    // Check for missing critical elements
    const missingElements = [];
    for (const [name, element] of Object.entries(criticalElements)) {
        if (!element) {
            console.error(`❌ Critical element missing: ${name}`);
            missingElements.push(name);
        }
    }

    if (missingElements.length > 0) {
        throw new Error(`Cannot initialize app. Missing critical elements: ${missingElements.join(', ')}`);
    }

    // Assign all elements to global variables
    getStartedBtn = criticalElements['get-started-btn'];
    chatInterface = criticalElements['chat-interface'];
    chatMessages = criticalElements['chat-messages'];
    queryInput = criticalElements['query-input'];
    themeToggleBtn = criticalElements['theme-toggle-btn'];

    // Optional Elements - These enhance functionality but aren't critical
    startChatBtn = document.getElementById('start-chat-btn');
    floatingChatBtn = document.getElementById('floating-chat-btn');
    watchDemoBtn = document.getElementById('watch-demo-btn');
    closeChatBtn = document.getElementById('close-chat');

    // Chat Interface Elements
    chatSidebar = document.getElementById('chat-sidebar');
    toggleSidebarBtn = document.getElementById('toggle-sidebar-btn');
    newChatBtn = document.getElementById('new-chat-btn');
    chatSearchInput = document.getElementById('chat-search-input');
    chatHistoryList = document.getElementById('chat-history-list');
    clearHistoryBtn = document.getElementById('clear-history-btn');

    // Chat Header Elements
    minimizeChatBtn = document.getElementById('minimize-chat-btn');
    maximizeChatBtn = document.getElementById('maximize-chat-btn');

    // Form Elements
    queryForm = document.getElementById('query-form');

    // Enhanced Input Elements
    attachBtn = document.getElementById('attach-btn');
    fileInput = document.getElementById('file-input');
    filePreview = document.getElementById('file-preview');
    voiceBtn = document.getElementById('voice-btn');
    voiceRecording = document.getElementById('voice-recording');
    emojiBtn = document.getElementById('emoji-btn');
    emojiPicker = document.getElementById('emoji-picker');
    emojiGrid = document.getElementById('emoji-grid');

    // Initialize Mermaid
    mermaid.initialize({
        startOnLoad: true,
        theme: document.documentElement.getAttribute('data-theme') === 'dark' ? 'dark' : 'default',
        securityLevel: 'loose',
        flowchart: {
            useMaxWidth: true,
            htmlLabels: true,
            curve: 'basis'
        }
    });

    console.log('✅ DOM Elements initialized successfully');
    return true;
}

// Initialize
async function updateStatus() {
    const health = await checkHealth();
    
    statusIndicators.innerHTML = Object.entries(health.components)
        .map(([name, status]) => createStatusIndicator(name, status))
        .join('');
}

// Add CSS animations via JavaScript for dynamic effects
const style = document.createElement('style');
style.textContent = `
    @keyframes ripple {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }
    
    .loaded .fade-in {
        animation: fadeInUp 0.6s ease forwards;
    }
`;
document.head.appendChild(style);

// Global variables
let animationController;
let chatController;
let ctaController;
let accessibilityController;
let themeManager;
let chatHistoryManager;
let fileAttachmentManager;
let voiceRecordingManager;
let emojiPickerManager;
let sidebarManager;

// Initialize Application
document.addEventListener('DOMContentLoaded', () => {
    try {
        console.log('🚀 Initializing Modern Educational RAG App...');

        // Initialize controllers
        window.chatController = new ChatController();
        
        // Handle "Start Chatting with AI" button click
        const startChatButton = document.getElementById('get-started-btn');
        if (startChatButton) {
            startChatButton.addEventListener('click', () => {
                window.chatController.showChat();
            });
        }

    // Add loading complete class to body
        document.body.classList.add('loaded');

        console.log('✅ App initialized successfully');
    } catch (error) {
        console.error('❌ Error initializing application:', error);
    }
});