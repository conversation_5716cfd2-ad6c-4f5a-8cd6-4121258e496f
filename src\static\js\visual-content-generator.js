/**
 * Visual Content Generator for Educational RAG
 * Automatically generates diagrams, flowcharts, and visual aids
 */

class VisualContentGenerator {
    constructor() {
        this.initializeMermaid();
        this.setupDiagramTemplates();
        this.diagramCounter = 0;
    }

    initializeMermaid() {
        if (typeof mermaid !== 'undefined') {
            mermaid.initialize({
                startOnLoad: false,
                theme: 'default',
                themeVariables: {
                    primaryColor: '#2563eb',
                    primaryTextColor: '#1e293b',
                    primaryBorderColor: '#3b82f6',
                    lineColor: '#64748b',
                    secondaryColor: '#f1f5f9',
                    tertiaryColor: '#e2e8f0',
                    background: '#ffffff',
                    mainBkg: '#ffffff',
                    secondBkg: '#f8fafc',
                    tertiaryBkg: '#f1f5f9'
                },
                flowchart: {
                    useMaxWidth: true,
                    htmlLabels: true,
                    curve: 'basis'
                },
                timeline: {
                    useMaxWidth: true
                },
                mindmap: {
                    useMaxWidth: true
                }
            });
        }
    }

    setupDiagramTemplates() {
        this.templates = {
            timeline: {
                pattern: /\b(\d{4}|\d{1,2}(?:st|nd|rd|th)?\s+(?:January|February|March|April|May|June|July|August|September|October|November|December)\s+\d{4})\b.*?(?:revolution|war|independence|treaty|battle|event)/gi,
                generator: this.generateTimeline.bind(this)
            },
            process: {
                pattern: /\b(?:steps?|process|procedure|method|how to|stages?)\b/gi,
                generator: this.generateProcessFlow.bind(this)
            },
            comparison: {
                pattern: /\b(?:compare|contrast|difference|similarity|versus|vs\.?)\b/gi,
                generator: this.generateComparison.bind(this)
            },
            causeEffect: {
                pattern: /\b(?:cause|effect|result|consequence|lead to|due to|because)\b/gi,
                generator: this.generateCauseEffect.bind(this)
            },
            conceptMap: {
                pattern: /\b(?:concept|relationship|connect|relate|link)\b/gi,
                generator: this.generateConceptMap.bind(this)
            },
            hierarchy: {
                pattern: /\b(?:structure|organization|hierarchy|levels?|classification)\b/gi,
                generator: this.generateHierarchy.bind(this)
            }
        };
    }

    analyzeContent(content, subject) {
        const analysis = {
            subject: subject,
            diagramTypes: [],
            extractedData: {},
            confidence: 0
        };

        // Check each diagram type
        Object.keys(this.templates).forEach(type => {
            const template = this.templates[type];
            const matches = content.match(template.pattern);
            
            if (matches && matches.length > 0) {
                analysis.diagramTypes.push({
                    type: type,
                    confidence: matches.length / 10, // Simple confidence scoring
                    matches: matches.length
                });
            }
        });

        // Sort by confidence
        analysis.diagramTypes.sort((a, b) => b.confidence - a.confidence);
        
        // Extract specific data based on top diagram type
        if (analysis.diagramTypes.length > 0) {
            const topType = analysis.diagramTypes[0].type;
            analysis.extractedData = this.extractDataForType(content, topType, subject);
            analysis.confidence = analysis.diagramTypes[0].confidence;
        }

        return analysis;
    }

    extractDataForType(content, type, subject) {
        switch (type) {
            case 'timeline':
                return this.extractTimelineData(content);
            case 'process':
                return this.extractProcessData(content);
            case 'comparison':
                return this.extractComparisonData(content);
            case 'causeEffect':
                return this.extractCauseEffectData(content);
            case 'conceptMap':
                return this.extractConceptData(content);
            case 'hierarchy':
                return this.extractHierarchyData(content);
            default:
                return {};
        }
    }

    extractTimelineData(content) {
        const datePattern = /\b(\d{4}|\d{1,2}(?:st|nd|rd|th)?\s+(?:January|February|March|April|May|June|July|August|September|October|November|December)\s+\d{4})\b[^.]*?([^.]+)/gi;
        const events = [];
        let match;

        while ((match = datePattern.exec(content)) !== null) {
            events.push({
                date: match[1],
                event: match[2].trim(),
                type: 'historical'
            });
        }

        return { events: events.slice(0, 8) }; // Limit to 8 events
    }

    extractProcessData(content) {
        const stepPattern = /(?:step\s*\d+|first|second|third|fourth|fifth|then|next|finally|lastly)[:\s]*([^.]+)/gi;
        const steps = [];
        let match;

        while ((match = stepPattern.exec(content)) !== null) {
            steps.push({
                title: match[1].trim(),
                description: ''
            });
        }

        return { steps: steps.slice(0, 6) }; // Limit to 6 steps
    }

    extractComparisonData(content) {
        // Simple extraction - could be enhanced with NLP
        const sentences = content.split(/[.!?]+/);
        const items = [];
        
        sentences.forEach(sentence => {
            if (sentence.match(/\b(?:while|whereas|but|however|on the other hand)\b/i)) {
                const parts = sentence.split(/\b(?:while|whereas|but|however|on the other hand)\b/i);
                if (parts.length >= 2) {
                    items.push({
                        item1: parts[0].trim(),
                        item2: parts[1].trim()
                    });
                }
            }
        });

        return { comparisons: items.slice(0, 4) };
    }

    extractCauseEffectData(content) {
        const causePattern = /(?:because|due to|caused by|as a result of)\s+([^.]+)/gi;
        const effectPattern = /(?:resulted in|led to|caused|effect was)\s+([^.]+)/gi;
        
        const causes = [];
        const effects = [];
        let match;

        while ((match = causePattern.exec(content)) !== null) {
            causes.push(match[1].trim());
        }

        while ((match = effectPattern.exec(content)) !== null) {
            effects.push(match[1].trim());
        }

        return {
            mainCause: causes[0] || 'Main Cause',
            effects: effects.slice(0, 5)
        };
    }

    extractConceptData(content) {
        // Extract key concepts (capitalized terms, quoted terms)
        const conceptPattern = /\b[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*\b|"([^"]+)"/g;
        const concepts = [];
        let match;

        while ((match = conceptPattern.exec(content)) !== null) {
            const concept = match[1] || match[0];
            if (concept.length > 3 && concept.length < 30) {
                concepts.push({
                    id: concepts.length + 1,
                    name: concept,
                    type: 'concept'
                });
            }
        }

        // Generate simple relationships
        const relationships = [];
        for (let i = 0; i < concepts.length - 1; i++) {
            relationships.push({
                source: concepts[i].id,
                target: concepts[i + 1].id,
                label: 'relates to'
            });
        }

        return {
            concepts: concepts.slice(0, 8),
            relationships: relationships.slice(0, 6)
        };
    }

    extractHierarchyData(content) {
        // Extract hierarchical information
        const levels = [];
        const lines = content.split('\n');
        
        lines.forEach(line => {
            const trimmed = line.trim();
            if (trimmed.match(/^\d+\./)) {
                levels.push({ level: 1, text: trimmed });
            } else if (trimmed.match(/^[a-z]\)/)) {
                levels.push({ level: 2, text: trimmed });
            } else if (trimmed.match(/^[ivx]+\./)) {
                levels.push({ level: 3, text: trimmed });
            }
        });

        return { hierarchy: levels.slice(0, 10) };
    }

    generateDiagram(content, subject, options = {}) {
        const analysis = this.analyzeContent(content, subject);
        
        if (analysis.diagramTypes.length === 0 || analysis.confidence < 0.3) {
            return null; // Not enough confidence to generate diagram
        }

        const topType = analysis.diagramTypes[0].type;
        const generator = this.templates[topType].generator;
        
        return generator(analysis.extractedData, subject, options);
    }

    generateTimeline(data, subject, options) {
        if (!data.events || data.events.length === 0) return null;

        const diagramId = `timeline-${++this.diagramCounter}`;
        const mermaidCode = `
timeline
    title ${subject.charAt(0).toUpperCase() + subject.slice(1)} Timeline
    ${data.events.map(event => `
    ${event.date} : ${event.event}`).join('')}
        `;

        return this.createDiagramContainer(diagramId, mermaidCode, 'timeline', subject);
    }

    generateProcessFlow(data, subject, options) {
        if (!data.steps || data.steps.length === 0) return null;

        const diagramId = `process-${++this.diagramCounter}`;
        const mermaidCode = `
flowchart TD
    ${data.steps.map((step, index) => {
        const nodeId = `step${index + 1}`;
        const nextNodeId = index < data.steps.length - 1 ? `step${index + 2}` : null;
        const connection = nextNodeId ? `\n    ${nodeId} --> ${nextNodeId}` : '';
        return `${nodeId}["${step.title}"]${connection}`;
    }).join('\n    ')}
        `;

        return this.createDiagramContainer(diagramId, mermaidCode, 'process', subject);
    }

    generateComparison(data, subject, options) {
        if (!data.comparisons || data.comparisons.length === 0) return null;

        const diagramId = `comparison-${++this.diagramCounter}`;
        const mermaidCode = `
graph LR
    subgraph "Comparison"
        ${data.comparisons.map((comp, index) => `
        A${index}["${comp.item1}"]
        B${index}["${comp.item2}"]
        A${index} -.vs.- B${index}`).join('\n        ')}
    end
        `;

        return this.createDiagramContainer(diagramId, mermaidCode, 'comparison', subject);
    }

    generateCauseEffect(data, subject, options) {
        if (!data.effects || data.effects.length === 0) return null;

        const diagramId = `cause-effect-${++this.diagramCounter}`;
        const mermaidCode = `
graph TD
    Cause["${data.mainCause}"]
    ${data.effects.map((effect, index) => `
    Effect${index + 1}["${effect}"]
    Cause --> Effect${index + 1}`).join('')}
        `;

        return this.createDiagramContainer(diagramId, mermaidCode, 'cause-effect', subject);
    }

    generateConceptMap(data, subject, options) {
        if (!data.concepts || data.concepts.length === 0) return null;

        const diagramId = `concept-map-${++this.diagramCounter}`;
        const mermaidCode = `
graph TB
    ${data.concepts.map(concept => `
    ${concept.id}["${concept.name}"]`).join('')}
    ${data.relationships.map(rel => `
    ${rel.source} --> ${rel.target}`).join('')}
        `;

        return this.createDiagramContainer(diagramId, mermaidCode, 'concept-map', subject);
    }

    generateHierarchy(data, subject, options) {
        if (!data.hierarchy || data.hierarchy.length === 0) return null;

        const diagramId = `hierarchy-${++this.diagramCounter}`;
        const mermaidCode = `
graph TD
    ${data.hierarchy.map((item, index) => {
        const nodeId = `node${index}`;
        const parentId = index > 0 ? `node${index - 1}` : null;
        const connection = parentId && item.level > data.hierarchy[index - 1].level ? 
            `\n    ${parentId} --> ${nodeId}` : '';
        return `${nodeId}["${item.text}"]${connection}`;
    }).join('\n    ')}
        `;

        return this.createDiagramContainer(diagramId, mermaidCode, 'hierarchy', subject);
    }

    createDiagramContainer(diagramId, mermaidCode, type, subject) {
        const container = document.createElement('div');
        container.className = `diagram-container diagram-${type} subject-${subject}`;
        
        const header = document.createElement('div');
        header.className = 'diagram-header';
        header.innerHTML = `
            <h4><i class="fas fa-chart-line"></i> ${this.getDiagramTitle(type)}</h4>
            <div class="diagram-controls">
                <button class="diagram-btn" onclick="this.closest('.diagram-container').querySelector('.mermaid').requestFullscreen()">
                    <i class="fas fa-expand"></i>
                </button>
                <button class="diagram-btn" onclick="window.visualContentGenerator.exportDiagram('${diagramId}')">
                    <i class="fas fa-download"></i>
                </button>
            </div>
        `;
        
        const diagramDiv = document.createElement('div');
        diagramDiv.id = diagramId;
        diagramDiv.className = 'mermaid';
        diagramDiv.textContent = mermaidCode.trim();
        
        container.appendChild(header);
        container.appendChild(diagramDiv);
        
        // Render the diagram
        setTimeout(() => {
            if (typeof mermaid !== 'undefined') {
                mermaid.render(diagramId + '-svg', mermaidCode.trim()).then(result => {
                    diagramDiv.innerHTML = result.svg;
                }).catch(error => {
                    console.error('Mermaid rendering error:', error);
                    diagramDiv.innerHTML = '<p class="diagram-error">Failed to render diagram</p>';
                });
            }
        }, 100);
        
        return container;
    }

    getDiagramTitle(type) {
        const titles = {
            timeline: 'Historical Timeline',
            process: 'Process Flow',
            comparison: 'Comparison Chart',
            'cause-effect': 'Cause & Effect',
            'concept-map': 'Concept Map',
            hierarchy: 'Hierarchical Structure'
        };
        return titles[type] || 'Diagram';
    }

    exportDiagram(diagramId) {
        const diagram = document.getElementById(diagramId);
        if (!diagram) return;

        const svg = diagram.querySelector('svg');
        if (!svg) return;

        // Create canvas and convert SVG to PNG
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        const data = new XMLSerializer().serializeToString(svg);
        const img = new Image();
        
        img.onload = () => {
            canvas.width = img.width;
            canvas.height = img.height;
            ctx.drawImage(img, 0, 0);
            
            // Download the image
            const link = document.createElement('a');
            link.download = `${diagramId}.png`;
            link.href = canvas.toDataURL();
            link.click();
        };
        
        img.src = 'data:image/svg+xml;base64,' + btoa(data);
    }

    updateTheme(theme) {
        const themeConfig = theme === 'dark' ? {
            theme: 'dark',
            themeVariables: {
                primaryColor: '#3b82f6',
                primaryTextColor: '#e2e8f0',
                primaryBorderColor: '#60a5fa',
                lineColor: '#94a3b8',
                secondaryColor: '#1e293b',
                tertiaryColor: '#334155',
                background: '#0f172a',
                mainBkg: '#1e293b',
                secondBkg: '#334155',
                tertiaryBkg: '#475569'
            }
        } : {
            theme: 'default',
            themeVariables: {
                primaryColor: '#2563eb',
                primaryTextColor: '#1e293b',
                primaryBorderColor: '#3b82f6',
                lineColor: '#64748b',
                secondaryColor: '#f1f5f9',
                tertiaryColor: '#e2e8f0',
                background: '#ffffff',
                mainBkg: '#ffffff',
                secondBkg: '#f8fafc',
                tertiaryBkg: '#f1f5f9'
            }
        };

        if (typeof mermaid !== 'undefined') {
            mermaid.initialize(themeConfig);
        }
    }
}

// Initialize global instance
window.visualContentGenerator = new VisualContentGenerator();

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = VisualContentGenerator;
}
