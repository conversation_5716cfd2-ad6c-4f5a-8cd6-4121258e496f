#!/usr/bin/env python3
"""
Enhanced Agentic RAG Service with Pinecone integration
Provides advanced RAG capabilities including chain-of-thought reasoning,
HOTs classification, and multi-modal responses.
"""

import os
import logging
from typing import Dict, Any, List, Optional
from dotenv import load_dotenv
from sentence_transformers import SentenceTransformer
import pinecone
from openai import OpenAI
import json
import time

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AgenticRAGService:
    """Advanced RAG service with agentic capabilities."""
    
    def __init__(self):
        """Initialize the agentic RAG service."""
        self.api_key = os.getenv('PINECONE_API_KEY')
        self.index_name = os.getenv('PINECONE_INDEX_NAME', 'virat-gyankosh-cbse')
        self.openai_api_key = os.getenv('OPENAI_API_KEY')
        
        self._init_embedding_model()
        self._init_pinecone()
        self._init_llm()
        
    def _init_embedding_model(self):
        """Initialize the embedding model."""
        try:
            self.embedding_model = SentenceTransformer('all-MiniLM-L6-v2')
            logger.info("✅ Embedding model initialized")
        except Exception as e:
            logger.error(f"❌ Failed to initialize embedding model: {e}")
            raise
    
    def _init_pinecone(self):
        """Initialize Pinecone connection."""
        try:
            import pinecone
            # Initialize Pinecone with new API
            self.pc = pinecone.Pinecone(api_key=self.api_key)
            self.index = self.pc.Index(self.index_name)

            # Test connection
            stats = self.index.describe_index_stats()
            logger.info(f"✅ Connected to Pinecone index: {self.index_name}")
            logger.info(f"📊 Index stats: {stats}")

        except Exception as e:
            logger.error(f"❌ Failed to initialize Pinecone: {e}")
            raise
    
    def _init_llm(self):
        """Initialize OpenAI LLM."""
        try:
            self.llm_client = OpenAI(api_key=self.openai_api_key)
            logger.info("✅ OpenAI LLM initialized")
        except Exception as e:
            logger.error(f"❌ Failed to initialize LLM: {e}")
            raise
    
    def classify_question_type(self, query: str) -> Dict[str, Any]:
        """Classify the question using Higher Order Thinking Skills (HOTs) framework."""
        classification_prompt = f"""
        Classify this educational question according to Bloom's Taxonomy and HOTs framework:
        
        Question: "{query}"
        
        Classify into one of these categories:
        1. REMEMBER: Recall facts, terms, basic concepts
        2. UNDERSTAND: Explain ideas or concepts  
        3. APPLY: Use information in new situations
        4. ANALYZE: Draw connections among ideas
        5. EVALUATE: Justify a stand or decision
        6. CREATE: Produce new or original work
        
        Also determine:
        - Subject: Geography, History, Economics, Political Science, or General
        - Complexity: Low, Medium, High
        - Response Type: Factual, Explanatory, Analytical, Creative
        
        Respond in JSON format:
        {{
            "bloom_level": "LEVEL",
            "subject": "SUBJECT", 
            "complexity": "COMPLEXITY",
            "response_type": "TYPE",
            "reasoning": "Brief explanation"
        }}
        """
        
        try:
            response = self.llm_client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[{"role": "user", "content": classification_prompt}],
                max_tokens=200,
                temperature=0.1
            )
            
            classification = json.loads(response.choices[0].message.content)
            return classification
            
        except Exception as e:
            logger.warning(f"Question classification failed: {e}")
            return {
                "bloom_level": "UNDERSTAND",
                "subject": "General",
                "complexity": "Medium", 
                "response_type": "Explanatory",
                "reasoning": "Default classification due to error"
            }
    
    def retrieve_context(self, query: str, top_k: int = 5) -> List[Dict[str, Any]]:
        """Retrieve relevant context from Pinecone."""
        try:
            # Generate query embedding
            query_embedding = self.embedding_model.encode([query], normalize_embeddings=True)[0]
            
            # Query Pinecone
            results = self.index.query(
                vector=query_embedding.tolist(),
                top_k=top_k,
                include_metadata=True
            )
            
            # Process results
            contexts = []
            for match in results.matches:
                if match.score > 0.3:  # Relevance threshold
                    contexts.append({
                        'text': match.metadata.get('text', ''),
                        'source': match.metadata.get('source', ''),
                        'subject': match.metadata.get('subject', ''),
                        'page': match.metadata.get('page', 0),
                        'score': float(match.score)
                    })
            
            return contexts
            
        except Exception as e:
            logger.error(f"Context retrieval failed: {e}")
            return []
    
    def generate_chain_of_thought_response(self, query: str, contexts: List[Dict], classification: Dict) -> str:
        """Generate response using chain-of-thought reasoning."""
        
        # Prepare context text
        context_text = "\n\n".join([
            f"Source: {ctx['source']} (Page {ctx['page']})\n{ctx['text']}"
            for ctx in contexts[:3]
        ])
        
        # Create specialized prompt based on question type
        if classification['bloom_level'] in ['ANALYZE', 'EVALUATE', 'CREATE']:
            reasoning_prompt = """
            Use step-by-step reasoning to answer this question. Follow this structure:
            1. **Understanding**: What is the question asking?
            2. **Analysis**: Break down the key concepts and relationships
            3. **Evidence**: What does the textbook evidence show?
            4. **Reasoning**: Connect the evidence to form logical conclusions
            5. **Answer**: Provide a comprehensive response
            """
        else:
            reasoning_prompt = """
            Provide a clear, structured answer following this approach:
            1. **Direct Answer**: Start with the main answer
            2. **Explanation**: Explain the key concepts
            3. **Examples**: Provide relevant examples from the textbook
            4. **Context**: Add important background information
            """
        
        system_prompt = f"""
        You are an expert CBSE Class 9 tutor specializing in {classification['subject']}.
        This question is classified as {classification['bloom_level']} level with {classification['complexity']} complexity.
        
        {reasoning_prompt}
        
        Guidelines:
        - Base your answer strictly on the provided textbook content
        - Use clear, student-friendly language appropriate for Class 9
        - Include specific examples and page references when possible
        - If information is insufficient, clearly state what you cannot answer
        """
        
        user_prompt = f"""
        Context from CBSE textbooks:
        {context_text}
        
        Question: {query}
        
        Please provide a comprehensive answer following the structured approach outlined above.
        """
        
        try:
            response = self.llm_client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                max_tokens=800,
                temperature=0.7
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            logger.error(f"Response generation failed: {e}")
            return "I apologize, but I encountered an error while generating the response. Please try again."
    
    def generate_learning_insights(self, query: str, classification: Dict) -> Dict[str, Any]:
        """Generate learning insights and recommendations."""
        insights = {
            "learning_objective": self._get_learning_objective(classification),
            "prerequisite_concepts": self._get_prerequisites(classification['subject']),
            "related_topics": self._get_related_topics(query, classification['subject']),
            "study_tips": self._get_study_tips(classification['bloom_level']),
            "practice_questions": self._generate_practice_questions(query, classification)
        }
        return insights
    
    def _get_learning_objective(self, classification: Dict) -> str:
        """Get learning objective based on classification."""
        objectives = {
            "REMEMBER": "Recall and recognize key facts and concepts",
            "UNDERSTAND": "Explain and interpret the main ideas",
            "APPLY": "Use knowledge in practical situations",
            "ANALYZE": "Break down complex information and identify relationships",
            "EVALUATE": "Make judgments and assess different perspectives",
            "CREATE": "Synthesize information to form new ideas"
        }
        return objectives.get(classification['bloom_level'], "Develop understanding of the topic")
    
    def _get_prerequisites(self, subject: str) -> List[str]:
        """Get prerequisite concepts for the subject."""
        prerequisites = {
            "Geography": ["Basic map reading", "Climate concepts", "Physical features"],
            "History": ["Timeline understanding", "Cause and effect", "Historical sources"],
            "Economics": ["Basic needs and wants", "Resources", "Production concepts"],
            "Political Science": ["Government basics", "Rights and duties", "Democratic principles"]
        }
        return prerequisites.get(subject, ["Basic reading comprehension"])
    
    def _get_related_topics(self, query: str, subject: str) -> List[str]:
        """Get related topics based on query and subject."""
        # This could be enhanced with semantic similarity
        topic_maps = {
            "Geography": ["Climate", "Natural resources", "Population", "Agriculture"],
            "History": ["Freedom struggle", "Colonial rule", "Social reforms", "Cultural heritage"],
            "Economics": ["Poverty", "Food security", "Globalization", "Development"],
            "Political Science": ["Democracy", "Elections", "Constitution", "Rights"]
        }
        return topic_maps.get(subject, ["General concepts"])
    
    def _get_study_tips(self, bloom_level: str) -> List[str]:
        """Get study tips based on Bloom's taxonomy level."""
        tips = {
            "REMEMBER": ["Use flashcards", "Create mnemonics", "Practice recall"],
            "UNDERSTAND": ["Explain in your own words", "Use concept maps", "Find examples"],
            "APPLY": ["Solve practice problems", "Use real-world examples", "Create scenarios"],
            "ANALYZE": ["Compare and contrast", "Identify patterns", "Break into parts"],
            "EVALUATE": ["Debate different viewpoints", "Assess pros and cons", "Make judgments"],
            "CREATE": ["Design projects", "Write essays", "Propose solutions"]
        }
        return tips.get(bloom_level, ["Review regularly", "Practice actively"])
    
    def _generate_practice_questions(self, query: str, classification: Dict) -> List[str]:
        """Generate practice questions related to the topic."""
        # Simplified version - could be enhanced with LLM generation
        return [
            f"Can you explain the main concepts related to this topic?",
            f"How does this relate to other topics in {classification['subject']}?",
            f"What are some real-world examples of this concept?"
        ]
    
    def process_query(self, query: str) -> Dict[str, Any]:
        """Process a query through the complete agentic RAG pipeline."""
        start_time = time.time()
        
        try:
            # Step 1: Classify the question
            classification = self.classify_question_type(query)
            logger.info(f"Question classified as: {classification['bloom_level']} - {classification['subject']}")
            
            # Step 2: Retrieve relevant context
            contexts = self.retrieve_context(query, top_k=5)
            logger.info(f"Retrieved {len(contexts)} relevant contexts")
            
            # Step 3: Generate response with chain-of-thought
            if contexts:
                answer = self.generate_chain_of_thought_response(query, contexts, classification)
                confidence = sum(ctx['score'] for ctx in contexts[:3]) / min(3, len(contexts))
            else:
                answer = "I couldn't find relevant information in the CBSE textbooks to answer your question. Please try rephrasing or ask about topics covered in Class 9 Geography, History, Economics, or Political Science."
                confidence = 0.0
            
            # Step 4: Generate learning insights
            insights = self.generate_learning_insights(query, classification)
            
            # Step 5: Prepare response
            processing_time = time.time() - start_time
            
            return {
                "answer": answer,
                "sources": [
                    {
                        "source": ctx['source'],
                        "page": ctx['page'],
                        "score": ctx['score'],
                        "subject": ctx['subject']
                    }
                    for ctx in contexts[:3]
                ],
                "confidence": confidence,
                "processing_time": processing_time,
                "classification": classification,
                "learning_insights": insights,
                "question_type": "agentic_rag"
            }
            
        except Exception as e:
            logger.error(f"Query processing failed: {e}")
            return {
                "answer": "I encountered an error while processing your question. Please try again.",
                "sources": [],
                "confidence": 0.0,
                "processing_time": time.time() - start_time,
                "classification": {"bloom_level": "ERROR", "subject": "Unknown"},
                "learning_insights": {},
                "question_type": "error"
            }
    
    def health_check(self) -> Dict[str, Any]:
        """Check the health of all agentic RAG components."""
        health_status = {
            "pinecone": self._check_pinecone_health(),
            "embedding_model": self._check_embedding_health(),
            "llm": self._check_llm_health(),
            "agentic_features": True
        }
        
        all_healthy = all(health_status.values())
        
        return {
            "status": "healthy" if all_healthy else "degraded",
            "components": health_status,
            "features": {
                "chain_of_thought": True,
                "hots_classification": True,
                "learning_insights": True,
                "pinecone_integration": health_status["pinecone"]
            }
        }
    
    def _check_pinecone_health(self) -> bool:
        """Check Pinecone connection health."""
        try:
            stats = self.index.describe_index_stats()
            return stats.total_vector_count > 0
        except Exception:
            return False
    
    def _check_embedding_health(self) -> bool:
        """Check embedding model health."""
        try:
            self.embedding_model.encode(["test"])
            return True
        except Exception:
            return False
    
    def _check_llm_health(self) -> bool:
        """Check LLM health."""
        try:
            self.llm_client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[{"role": "user", "content": "test"}],
                max_tokens=5
            )
            return True
        except Exception:
            return False

    def get_topic_content(self, subject: str, topic: str) -> Dict[str, Any]:
        """Get structured content for diagram generation."""
        try:
            # Get relevant chunks from vector store
            query = f"{subject} {topic}"
            relevant_chunks = self.get_relevant_chunks(query)
            
            # Extract and structure content based on topic
            content = self.structure_content(relevant_chunks, topic)
            
            return content
        except Exception as e:
            logger.error(f"Error getting topic content: {str(e)}")
            return {}
    
    def structure_content(self, chunks: List[Dict[str, Any]], topic: str) -> Dict[str, Any]:
        """Structure content for different diagram types."""
        try:
            # Combine text from chunks
            combined_text = " ".join([chunk.get("text", "") for chunk in chunks])
            
            # Use GPT to extract structured content
            prompt = f"""
            Extract structured information about "{topic}" from the following text for visualization.
            Include categories, comparison items, timeline events, concepts, relationships, causes, and effects.
            Format the response as JSON with the following structure:
            {{
                "categories": ["category1", "category2", ...],
                "comparison_items": [
                    {{
                        "name": "item1",
                        "comparisons": [
                            {{"value": "value1", "type": "category1"}},
                            {{"value": "value2", "type": "category2"}}
                        ]
                    }}
                ],
                "timeline_events": [
                    {{"date": "date1", "title": "event1", "type": "type1"}},
                    {{"date": "date2", "title": "event2", "type": "type2"}}
                ],
                "concepts": [
                    {{"id": 1, "name": "concept1", "type": "type1"}},
                    {{"id": 2, "name": "concept2", "type": "type2"}}
                ],
                "relationships": [
                    {{"source": 1, "target": 2, "label": "relates to"}}
                ],
                "main_cause": "main cause",
                "effects": ["effect1", "effect2"]
            }}
            
            Text: {combined_text}
            """
            
            response = self.llm_client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[{"role": "user", "content": prompt}],
                temperature=0.7,
                max_tokens=1000
            )
            
            # Parse the structured content
            content = json.loads(response.choices[0].message.content)
            return content
            
        except Exception as e:
            logger.error(f"Error structuring content: {str(e)}")
            return {}
