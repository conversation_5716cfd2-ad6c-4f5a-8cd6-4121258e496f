#!/usr/bin/env python3
"""
Enhanced Agentic RAG Service with Pinecone integration
Provides advanced RAG capabilities including chain-of-thought reasoning,
HOTs classification, and multi-modal responses.
"""

import os
import logging
from typing import Dict, Any, List, Optional
from dotenv import load_dotenv
from sentence_transformers import SentenceTransformer
import pinecone
from openai import OpenAI
import json
import time

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AgenticRAGService:
    """Advanced RAG service with agentic capabilities."""
    
    def __init__(self):
        """Initialize the agentic RAG service."""
        self.api_key = os.getenv('PINECONE_API_KEY')
        self.index_name = os.getenv('PINECONE_INDEX_NAME', 'virat-gyankosh-cbse')
        self.openai_api_key = os.getenv('OPENAI_API_KEY')
        
        self._init_embedding_model()
        self._init_pinecone()
        self._init_llm()
        
    def _init_embedding_model(self):
        """Initialize the embedding model."""
        try:
            self.embedding_model = SentenceTransformer('all-MiniLM-L6-v2')
            logger.info("✅ Embedding model initialized")
        except Exception as e:
            logger.error(f"❌ Failed to initialize embedding model: {e}")
            raise
    
    def _init_pinecone(self):
        """Initialize Pinecone connection."""
        try:
            import pinecone
            # Initialize Pinecone with new API
            self.pc = pinecone.Pinecone(api_key=self.api_key)
            self.index = self.pc.Index(self.index_name)

            # Test connection
            stats = self.index.describe_index_stats()
            logger.info(f"✅ Connected to Pinecone index: {self.index_name}")
            logger.info(f"📊 Index stats: {stats}")

        except Exception as e:
            logger.error(f"❌ Failed to initialize Pinecone: {e}")
            raise
    
    def _init_llm(self):
        """Initialize OpenAI LLM."""
        try:
            self.llm_client = OpenAI(api_key=self.openai_api_key)
            logger.info("✅ OpenAI LLM initialized")
        except Exception as e:
            logger.error(f"❌ Failed to initialize LLM: {e}")
            raise
    
    def classify_question_type(self, query: str) -> Dict[str, Any]:
        """Classify the question using Higher Order Thinking Skills (HOTs) framework."""
        classification_prompt = f"""
        Classify this educational question according to Bloom's Taxonomy and HOTs framework:
        
        Question: "{query}"
        
        Classify into one of these categories:
        1. REMEMBER: Recall facts, terms, basic concepts
        2. UNDERSTAND: Explain ideas or concepts  
        3. APPLY: Use information in new situations
        4. ANALYZE: Draw connections among ideas
        5. EVALUATE: Justify a stand or decision
        6. CREATE: Produce new or original work
        
        Also determine:
        - Subject: Geography, History, Economics, Political Science, or General
        - Complexity: Low, Medium, High
        - Response Type: Factual, Explanatory, Analytical, Creative
        
        Respond in JSON format:
        {{
            "bloom_level": "LEVEL",
            "subject": "SUBJECT", 
            "complexity": "COMPLEXITY",
            "response_type": "TYPE",
            "reasoning": "Brief explanation"
        }}
        """
        
        try:
            response = self.llm_client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[{"role": "user", "content": classification_prompt}],
                max_tokens=200,
                temperature=0.1
            )
            
            classification = json.loads(response.choices[0].message.content)
            return classification
            
        except Exception as e:
            logger.warning(f"Question classification failed: {e}")
            return {
                "bloom_level": "UNDERSTAND",
                "subject": "General",
                "complexity": "Medium", 
                "response_type": "Explanatory",
                "reasoning": "Default classification due to error"
            }
    
    def retrieve_context(self, query: str, top_k: int = 5) -> List[Dict[str, Any]]:
        """Retrieve relevant context from Pinecone."""
        try:
            # Generate query embedding
            query_embedding = self.embedding_model.encode([query], normalize_embeddings=True)[0]
            
            # Query Pinecone
            results = self.index.query(
                vector=query_embedding.tolist(),
                top_k=top_k,
                include_metadata=True
            )
            
            # Process results
            contexts = []
            for match in results.matches:
                if match.score > 0.3:  # Relevance threshold
                    contexts.append({
                        'text': match.metadata.get('text', ''),
                        'source': match.metadata.get('source', ''),
                        'subject': match.metadata.get('subject', ''),
                        'page': match.metadata.get('page', 0),
                        'score': float(match.score)
                    })
            
            return contexts
            
        except Exception as e:
            logger.error(f"Context retrieval failed: {e}")
            return []
    
    def generate_enhanced_response(self, query: str, contexts: List[Dict], classification: Dict) -> str:
        """Generate enhanced response with rich formatting and educational structure."""

        # Prepare context text
        context_text = "\n\n".join([
            f"Source: {ctx['source']} (Page {ctx['page']})\n{ctx['text']}"
            for ctx in contexts[:3]
        ])

        # Create enhanced prompt based on question type and subject
        subject = classification.get('subject', 'General').lower()
        bloom_level = classification.get('bloom_level', 'UNDERSTAND')

        if bloom_level in ['ANALYZE', 'EVALUATE', 'CREATE']:
            structure_prompt = self._get_analytical_structure(subject)
        else:
            structure_prompt = self._get_explanatory_structure(subject)

        # Get comprehensive formatting template
        formatting_template = self._get_professional_formatting_template(subject, bloom_level)

        # Get academic standards and citation requirements
        academic_standards = self._get_academic_formatting_standards()

        # Get visual elements template
        visual_elements = self._get_visual_elements_template(subject)

        system_prompt = f"""
        You are an advanced educational AI assistant specializing in CBSE curriculum content with professional response formatting capabilities. Your responses must follow strict formatting guidelines to ensure optimal educational presentation and user experience.

        MANDATORY RESPONSE FORMATTING PROTOCOL:

        1. CONTENT STRUCTURE HIERARCHY:
           - Use ## for main topic headings (e.g., ## **DEMOCRATIC INSTITUTIONS**)
           - Use ### for subtopic headings (e.g., ### **Parliamentary System**)
           - Use #### for detailed sections (e.g., #### ***Key Features***)
           - Apply **bold formatting** for all key concepts and definitions
           - Use *italic formatting* for proper nouns, dates, and technical terms
           - Implement > blockquotes for important definitions and key points

        2. EDUCATIONAL CONTENT FORMATTING:
           - Begin responses with brief contextual overview in regular text
           - Structure content using numbered lists for processes and procedures
           - Use bullet points for feature lists and characteristics
           - Apply ✓ checkmarks for summary points and key takeaways
           - Include transitional phrases between major content sections

        3. VISUAL ENHANCEMENT REQUIREMENTS:
           - Create ASCII tables for comparisons and data presentation
           - Generate simple text-based diagrams for process explanations
           - Use horizontal rules (---) to separate major content sections
           - Implement proper paragraph spacing with double line breaks
           - Apply consistent indentation for hierarchical information

        SUBJECT SPECIALIZATION: {classification['subject']}
        QUESTION COMPLEXITY: {classification['bloom_level']} level with {classification['complexity']} complexity

        {structure_prompt}

        {formatting_template}

        {academic_standards}

        {visual_elements}

        MANDATORY RESPONSE REQUIREMENTS:
        - Base your answer strictly on the provided textbook content
        - Use clear, student-friendly language appropriate for Class 9
        - Include specific page references with [Page X] citations
        - Follow the exact formatting protocol outlined above
        - Ensure all proper nouns are italicized (*India*, *Gandhi*, *Parliament*)
        - Use hierarchical heading structure consistently
        - Apply professional typography standards throughout
        - Include ✓ checkmarks for summary points and key takeaways
        - Create visual elements (tables, diagrams) when appropriate
        - Maintain proper paragraph spacing and visual structure
        """
        
        user_prompt = f"""
        Context from CBSE textbooks:
        {context_text}

        Question: {query}

        Please provide a comprehensive answer following the structured approach outlined above.
        """

        try:
            response = self.llm_client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                max_tokens=1200,  # Increased for richer content
                temperature=0.7
            )

            return response.choices[0].message.content

        except Exception as e:
            logger.error(f"Response generation failed: {e}")
            return "I apologize, but I encountered an error while generating the response. Please try again."

    def _get_analytical_structure(self, subject: str) -> str:
        """Get analytical response structure based on subject."""
        structures = {
            'history': """
            Use this analytical structure for historical topics:
            ## Understanding the Question
            - Identify the historical period, events, or figures involved

            ## Historical Analysis
            - **Context**: Historical background and setting
            - **Causes**: Primary and secondary factors
            - **Events**: Chronological sequence of key developments
            - **Consequences**: Short-term and long-term effects

            ## Evidence from Sources
            - Quote specific examples from textbook
            - Reference dates, names, and places

            ## Conclusion
            - Synthesize the analysis
            - Connect to broader historical themes
            """,
            'geography': """
            Use this analytical structure for geographical topics:
            ## Understanding the Question
            - Identify the geographical concept or region

            ## Geographical Analysis
            - **Location**: Where is it located?
            - **Physical Features**: Landforms, climate, etc.
            - **Human Interaction**: How people interact with environment
            - **Processes**: Natural and human processes involved

            ## Evidence and Examples
            - Specific examples from different regions
            - Statistical data and measurements

            ## Conclusion
            - Summarize key geographical relationships
            """,
            'economics': """
            Use this analytical structure for economic topics:
            ## Understanding the Question
            - Identify the economic concept or issue

            ## Economic Analysis
            - **Definition**: Clear definition of economic terms
            - **Factors**: Key economic factors involved
            - **Relationships**: How different elements interact
            - **Impact**: Effects on different groups/sectors

            ## Evidence and Data
            - Statistical information from textbook
            - Real-world examples and case studies

            ## Conclusion
            - Economic implications and significance
            """,
            'politics': """
            Use this analytical structure for political topics:
            ## Understanding the Question
            - Identify the political concept or institution

            ## Political Analysis
            - **Structure**: How the system/institution is organized
            - **Functions**: What roles it plays
            - **Processes**: How it operates
            - **Relationships**: Connections with other institutions

            ## Evidence and Examples
            - Constitutional provisions
            - Real examples from Indian democracy

            ## Conclusion
            - Significance for democratic governance
            """
        }
        return structures.get(subject, structures['history'])

    def _get_explanatory_structure(self, subject: str) -> str:
        """Get explanatory response structure based on subject."""
        return f"""
        Use this explanatory structure for {subject} topics:
        ## Direct Answer
        - Start with a clear, direct answer to the question

        ## Key Concepts
        - **Definition**: Define important terms
        - **Explanation**: Explain the main concepts clearly

        ## Examples and Evidence
        - Provide specific examples from the textbook
        - Include relevant facts, figures, and case studies

        ## Additional Context
        - Background information that helps understanding
        - Connections to other related topics

        ## Summary
        - Recap the main points in simple terms
        """

    def _get_professional_formatting_template(self, subject: str, bloom_level: str) -> str:
        """Get comprehensive professional formatting template."""
        return f"""
        MANDATORY FORMATTING REQUIREMENTS FOR {subject.upper()} RESPONSES:

        1. TYPOGRAPHY HIERARCHY:
           - Main headings: **BOLD ALL CAPS** (e.g., **DEMOCRACY AND ELECTIONS**)
           - Subheadings: **Bold Title Case** (e.g., **Key Features of Democracy**)
           - Section headers: ***Bold Italic*** (e.g., ***Historical Context***)
           - Important terms: *italics* for all proper nouns, dates, and key concepts
           - Definitions: Use > blockquotes for formal definitions
           - Examples: Use numbered lists with clear indentation

        2. CONTENT STRUCTURE TEMPLATE:
           ```
           ## **MAIN TOPIC HEADING**

           ### Brief Overview
           [Introductory paragraph with key context]

           ### **Core Concepts**
           1. **Primary Concept**: *Definition* with supporting explanation
           2. **Secondary Concept**: *Definition* with supporting explanation

           ### **Detailed Explanation**
           > **Key Definition**: [Formal definition in blockquote]

           [Detailed explanation with proper noun emphasis and academic formatting]

           ### **Visual Representation**
           [ASCII diagram or flowchart representation when applicable]

           ### **Practical Applications**
           - Real-world example 1
           - Real-world example 2
           - Historical context or case study

           ### **Summary Points**
           ✓ Key takeaway 1
           ✓ Key takeaway 2
           ✓ Key takeaway 3

           **Sources**: [Textbook references with page numbers]
           ```

        3. SUBJECT-SPECIFIC FORMATTING:
           - {self._get_subject_specific_formatting(subject)}
        """

    def _get_subject_specific_formatting(self, subject: str) -> str:
        """Get subject-specific formatting guidelines."""
        formatting_rules = {
            'history': """
            - Emphasize chronological organization with timeline elements
            - Use *italics* for all historical figures, events, and dates
            - Create comparison tables for different periods/civilizations
            - Include cause-effect flowcharts for major events
            - Format dates as **15th August 1947** or ***1789 CE***
            """,
            'geography': """
            - Incorporate spatial relationships and map references
            - Use *italics* for geographical features, places, and coordinates
            - Create ASCII maps or directional diagrams when relevant
            - Include measurement data in formatted tables
            - Emphasize climate, vegetation, and physical features
            """,
            'economics': """
            - Highlight numerical data and statistical representations
            - Use tables for economic comparisons and data
            - Format currency and percentages consistently
            - Create flowcharts for economic processes
            - Emphasize cause-effect relationships in economic phenomena
            """,
            'politics': """
            - Emphasize institutional relationships and governmental structures
            - Use *italics* for constitutional terms and institutional names
            - Create organizational charts for government structure
            - Include comparison tables for different political systems
            - Format legal provisions and articles consistently
            """
        }
        return formatting_rules.get(subject.lower(), "Apply general academic formatting standards")

    def _get_academic_formatting_standards(self) -> str:
        """Get academic formatting standards and citation requirements."""
        return """
        ACADEMIC FORMATTING STANDARDS:

        1. CITATION FORMAT:
           - Always cite sources with [Page X] references
           - Format as: "According to the textbook [Page 45], democracy is..."
           - Include multiple page references when drawing from different sections

        2. ACADEMIC LANGUAGE:
           - Use proper academic terminology and vocabulary
           - Maintain consistent tense and voice throughout
           - Avoid colloquial expressions and informal language

        3. CROSS-REFERENCES:
           - Include references to related topics
           - Use phrases like "As discussed in the chapter on..."
           - Connect concepts across different subjects when relevant

        4. QUALITY ASSURANCE:
           - Ensure all proper nouns receive italic formatting
           - Verify all headings follow established hierarchy
           - Confirm all citations maintain proper academic format
           - Check for formatting consistency throughout response
        """

    def _get_visual_elements_template(self, subject: str) -> str:
        """Get visual elements template for the subject."""
        return f"""
        VISUAL ELEMENTS INTEGRATION FOR {subject.upper()}:

        1. ASCII DIAGRAMS:
           - Create simple flowcharts using text-based diagrams
           - Use arrows (→, ↓, ↑) to show relationships
           - Include process flows for complex concepts

        2. TABLE GENERATION:
           - Use markdown tables for comparisons
           - Include headers with proper formatting
           - Align data consistently

        3. TIMELINE REPRESENTATIONS:
           - For historical content, create chronological timelines
           - Format as: **Year** → *Event* → **Consequence**

        4. HIERARCHICAL STRUCTURES:
           - Use indented lists for organizational structures
           - Show relationships with proper nesting
           - Include connecting elements where appropriate

        EXAMPLE TABLE FORMAT:
        | **Aspect** | **Feature 1** | **Feature 2** |
        |------------|---------------|---------------|
        | *Property* | Description   | Description   |

        EXAMPLE FLOWCHART:
        ```
        **Start** → *Process 1* → *Decision* → **End Result**
                                     ↓
                                *Alternative Path*
        ```
        """
    
    def generate_learning_insights(self, query: str, classification: Dict) -> Dict[str, Any]:
        """Generate learning insights and recommendations."""
        insights = {
            "learning_objective": self._get_learning_objective(classification),
            "prerequisite_concepts": self._get_prerequisites(classification['subject']),
            "related_topics": self._get_related_topics(query, classification['subject']),
            "study_tips": self._get_study_tips(classification['bloom_level']),
            "practice_questions": self._generate_practice_questions(query, classification)
        }
        return insights
    
    def _get_learning_objective(self, classification: Dict) -> str:
        """Get learning objective based on classification."""
        objectives = {
            "REMEMBER": "Recall and recognize key facts and concepts",
            "UNDERSTAND": "Explain and interpret the main ideas",
            "APPLY": "Use knowledge in practical situations",
            "ANALYZE": "Break down complex information and identify relationships",
            "EVALUATE": "Make judgments and assess different perspectives",
            "CREATE": "Synthesize information to form new ideas"
        }
        return objectives.get(classification['bloom_level'], "Develop understanding of the topic")
    
    def _get_prerequisites(self, subject: str) -> List[str]:
        """Get prerequisite concepts for the subject."""
        prerequisites = {
            "Geography": ["Basic map reading", "Climate concepts", "Physical features"],
            "History": ["Timeline understanding", "Cause and effect", "Historical sources"],
            "Economics": ["Basic needs and wants", "Resources", "Production concepts"],
            "Political Science": ["Government basics", "Rights and duties", "Democratic principles"]
        }
        return prerequisites.get(subject, ["Basic reading comprehension"])
    
    def _get_related_topics(self, query: str, subject: str) -> List[str]:
        """Get related topics based on query and subject."""
        # This could be enhanced with semantic similarity
        topic_maps = {
            "Geography": ["Climate", "Natural resources", "Population", "Agriculture"],
            "History": ["Freedom struggle", "Colonial rule", "Social reforms", "Cultural heritage"],
            "Economics": ["Poverty", "Food security", "Globalization", "Development"],
            "Political Science": ["Democracy", "Elections", "Constitution", "Rights"]
        }
        return topic_maps.get(subject, ["General concepts"])
    
    def _get_study_tips(self, bloom_level: str) -> List[str]:
        """Get study tips based on Bloom's taxonomy level."""
        tips = {
            "REMEMBER": ["Use flashcards", "Create mnemonics", "Practice recall"],
            "UNDERSTAND": ["Explain in your own words", "Use concept maps", "Find examples"],
            "APPLY": ["Solve practice problems", "Use real-world examples", "Create scenarios"],
            "ANALYZE": ["Compare and contrast", "Identify patterns", "Break into parts"],
            "EVALUATE": ["Debate different viewpoints", "Assess pros and cons", "Make judgments"],
            "CREATE": ["Design projects", "Write essays", "Propose solutions"]
        }
        return tips.get(bloom_level, ["Review regularly", "Practice actively"])
    
    def _generate_practice_questions(self, query: str, classification: Dict) -> List[str]:
        """Generate practice questions related to the topic."""
        # Simplified version - could be enhanced with LLM generation
        return [
            f"Can you explain the main concepts related to this topic?",
            f"How does this relate to other topics in {classification['subject']}?",
            f"What are some real-world examples of this concept?"
        ]
    
    def process_query(self, query: str) -> Dict[str, Any]:
        """Process a query through the complete agentic RAG pipeline."""
        start_time = time.time()
        
        try:
            # Step 1: Classify the question
            classification = self.classify_question_type(query)
            logger.info(f"Question classified as: {classification['bloom_level']} - {classification['subject']}")
            
            # Step 2: Retrieve relevant context
            contexts = self.retrieve_context(query, top_k=5)
            logger.info(f"Retrieved {len(contexts)} relevant contexts")
            
            # Step 3: Generate enhanced response with rich formatting
            if contexts:
                answer = self.generate_enhanced_response(query, contexts, classification)
                confidence = sum(ctx['score'] for ctx in contexts[:3]) / min(3, len(contexts))
            else:
                answer = "I couldn't find relevant information in the CBSE textbooks to answer your question. Please try rephrasing or ask about topics covered in Class 9 Geography, History, Economics, or Political Science."
                confidence = 0.0
            
            # Step 4: Generate learning insights
            insights = self.generate_learning_insights(query, classification)
            
            # Step 5: Prepare response
            processing_time = time.time() - start_time
            
            return {
                "answer": answer,
                "sources": [
                    {
                        "source": ctx['source'],
                        "page": ctx['page'],
                        "score": ctx['score'],
                        "subject": ctx['subject']
                    }
                    for ctx in contexts[:3]
                ],
                "confidence": confidence,
                "processing_time": processing_time,
                "classification": classification,
                "learning_insights": insights,
                "question_type": "agentic_rag"
            }
            
        except Exception as e:
            logger.error(f"Query processing failed: {e}")
            return {
                "answer": "I encountered an error while processing your question. Please try again.",
                "sources": [],
                "confidence": 0.0,
                "processing_time": time.time() - start_time,
                "classification": {"bloom_level": "ERROR", "subject": "Unknown"},
                "learning_insights": {},
                "question_type": "error"
            }
    
    def health_check(self) -> Dict[str, Any]:
        """Check the health of all agentic RAG components."""
        health_status = {
            "pinecone": self._check_pinecone_health(),
            "embedding_model": self._check_embedding_health(),
            "llm": self._check_llm_health(),
            "agentic_features": True
        }
        
        all_healthy = all(health_status.values())
        
        return {
            "status": "healthy" if all_healthy else "degraded",
            "components": health_status,
            "features": {
                "chain_of_thought": True,
                "hots_classification": True,
                "learning_insights": True,
                "pinecone_integration": health_status["pinecone"]
            }
        }
    
    def _check_pinecone_health(self) -> bool:
        """Check Pinecone connection health."""
        try:
            stats = self.index.describe_index_stats()
            return stats.total_vector_count > 0
        except Exception:
            return False
    
    def _check_embedding_health(self) -> bool:
        """Check embedding model health."""
        try:
            self.embedding_model.encode(["test"])
            return True
        except Exception:
            return False
    
    def _check_llm_health(self) -> bool:
        """Check LLM health."""
        try:
            self.llm_client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[{"role": "user", "content": "test"}],
                max_tokens=5
            )
            return True
        except Exception:
            return False

    def get_topic_content(self, subject: str, topic: str) -> Dict[str, Any]:
        """Get structured content for diagram generation."""
        try:
            # Get relevant chunks from vector store
            query = f"{subject} {topic}"
            relevant_chunks = self.get_relevant_chunks(query)
            
            # Extract and structure content based on topic
            content = self.structure_content(relevant_chunks, topic)
            
            return content
        except Exception as e:
            logger.error(f"Error getting topic content: {str(e)}")
            return {}
    
    def structure_content(self, chunks: List[Dict[str, Any]], topic: str) -> Dict[str, Any]:
        """Structure content for different diagram types."""
        try:
            # Combine text from chunks
            combined_text = " ".join([chunk.get("text", "") for chunk in chunks])
            
            # Use GPT to extract structured content
            prompt = f"""
            Extract structured information about "{topic}" from the following text for visualization.
            Include categories, comparison items, timeline events, concepts, relationships, causes, and effects.
            Format the response as JSON with the following structure:
            {{
                "categories": ["category1", "category2", ...],
                "comparison_items": [
                    {{
                        "name": "item1",
                        "comparisons": [
                            {{"value": "value1", "type": "category1"}},
                            {{"value": "value2", "type": "category2"}}
                        ]
                    }}
                ],
                "timeline_events": [
                    {{"date": "date1", "title": "event1", "type": "type1"}},
                    {{"date": "date2", "title": "event2", "type": "type2"}}
                ],
                "concepts": [
                    {{"id": 1, "name": "concept1", "type": "type1"}},
                    {{"id": 2, "name": "concept2", "type": "type2"}}
                ],
                "relationships": [
                    {{"source": 1, "target": 2, "label": "relates to"}}
                ],
                "main_cause": "main cause",
                "effects": ["effect1", "effect2"]
            }}
            
            Text: {combined_text}
            """
            
            response = self.llm_client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[{"role": "user", "content": prompt}],
                temperature=0.7,
                max_tokens=1000
            )
            
            # Parse the structured content
            content = json.loads(response.choices[0].message.content)
            return content
            
        except Exception as e:
            logger.error(f"Error structuring content: {str(e)}")
            return {}
